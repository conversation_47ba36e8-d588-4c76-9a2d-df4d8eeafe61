#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Novashare\n"
"POT-Creation-Date: 2025-04-10 13:13-0700\n"
"PO-Revision-Date: 2025-04-10 13:13-0700\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: novashare.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#. translators: the plugin name.
#: Novashare_Plugin_Updater.php:257
#, php-format
msgid "There is a new version of %1$s available."
msgstr ""

#: Novashare_Plugin_Updater.php:263
msgid "Contact your network administrator to install the update."
msgstr ""

#. translators: 1. opening anchor tag, do not translate 2. the new plugin version 3. closing anchor tag, do not translate.
#: Novashare_Plugin_Updater.php:268
#, php-format
msgid "%1$sView version %2$s details%3$s."
msgstr ""

#: Novashare_Plugin_Updater.php:276
#, php-format
msgid "%1$sView version %2$s details%3$s or %4$supdate now%5$s."
msgstr ""

#: Novashare_Plugin_Updater.php:287
msgid "Update now."
msgstr ""

#: Novashare_Plugin_Updater.php:519
msgid "You do not have permission to install plugin updates"
msgstr ""

#: Novashare_Plugin_Updater.php:519
msgid "Error"
msgstr ""

#: inc/admin.php:33 inc/settings.php:13
msgid "Inline Content"
msgstr ""

#: inc/admin.php:34 inc/settings.php:494
msgid "Floating Bar"
msgstr ""

#: inc/admin.php:35 inc/settings.php:1061
msgid "Share Button"
msgstr ""

#: inc/admin.php:36 inc/click_to_tweet.php:140 inc/settings.php:1226
#: inc/settings.php:1260 js/blocks.js:32 novashare.php:329
msgid "Click to Post"
msgstr ""

#: inc/admin.php:37 inc/settings.php:1342
msgid "Highlight"
msgstr ""

#: inc/admin.php:38 inc/settings.php:1491
msgid "Pinterest"
msgstr ""

#: inc/admin.php:39 inc/settings.php:1730
msgid "Configuration"
msgstr ""

#: inc/admin.php:45 inc/settings.php:2217
msgid "Tools"
msgstr ""

#: inc/admin.php:50
msgid "Network"
msgstr ""

#: inc/admin.php:55 inc/license.php:67
msgid "License"
msgstr ""

#: inc/admin.php:59 inc/support.php:5 novashare.php:414
msgid "Support"
msgstr ""

#: inc/admin.php:68
#, no-php-format
msgid "Get 25% off our performance plugin."
msgstr ""

#: inc/admin.php:79
msgid "Version"
msgstr ""

#: inc/admin.php:154
msgid "Save Changes"
msgstr ""

#: inc/classes/Ajax.php:34
msgid "Settings saved."
msgstr ""

#: inc/classes/Ajax.php:50
msgid "Successfully restored default options."
msgstr ""

#: inc/classes/Ajax.php:66
msgid "Settings exported."
msgstr ""

#: inc/classes/Ajax.php:83
msgid "No import file given."
msgstr ""

#: inc/classes/Ajax.php:92
msgid "Please upload a valid .json file."
msgstr ""

#: inc/classes/Ajax.php:108
msgid "Successfully imported Novashare settings."
msgstr ""

#: inc/classes/Ajax.php:129
msgid "Share counts purged."
msgstr ""

#: inc/classes/Ajax.php:134
msgid "Share counts not purged."
msgstr ""

#: inc/classes/Ajax.php:152
msgid "Short links purged."
msgstr ""

#: inc/classes/Ajax.php:158
msgid "Short links not purged."
msgstr ""

#: inc/classes/Ajax.php:177
msgid "Permission denied."
msgstr ""

#: inc/classes/Ajax.php:184
msgid "Nonce is invalid."
msgstr ""

#: inc/click_to_tweet.php:155 inc/functions.php:1371
#: inc/functions_pinterest.php:365
msgid "Share on"
msgstr ""

#: inc/functions.php:266
msgid "Subscribe"
msgstr ""

#: inc/functions.php:1120
msgid "URL Copied"
msgstr ""

#: inc/functions.php:1291 js/blocks-share.js:381
msgid "SHARES"
msgstr ""

#: inc/functions.php:1291
msgid "SHARE"
msgstr ""

#: inc/functions.php:1317 inc/settings.php:1092
msgid "Share to..."
msgstr ""

#: inc/functions.php:1339
msgid "Your Mastodon Instance"
msgstr ""

#: inc/functions.php:1359
msgid "Copy share link"
msgstr ""

#: inc/functions.php:1362
msgid "Share via"
msgstr ""

#: inc/functions.php:1365
msgid "Print this page"
msgstr ""

#: inc/functions.php:1368
msgid "Share on more networks"
msgstr ""

#: inc/functions_network.php:18
msgid "Network Access"
msgstr ""

#: inc/functions_network.php:23
msgid "Choose who has access to manage Novashare plugin settings."
msgstr ""

#: inc/functions_network.php:30
msgid "Network Default"
msgstr ""

#: inc/functions_network.php:35
msgid "Choose a subsite that you want to pull default settings from."
msgstr ""

#: inc/functions_network.php:42 inc/settings.php:2224
msgid "Clean Uninstall"
msgstr ""

#: inc/functions_network.php:49
msgid ""
"When enabled, this will cause all Novashare options data to be removed from "
"your database when the plugin is uninstalled."
msgstr ""

#: inc/functions_network.php:62
msgid "Site Admins (Default)"
msgstr ""

#: inc/functions_network.php:63
msgid "Super Admins Only"
msgstr ""

#: inc/functions_network.php:79
msgid "None"
msgstr ""

#: inc/functions_pinterest.php:85 inc/functions_pinterest.php:185
#: js/blocks-pinterest.js:154
msgid "Pin Title"
msgstr ""

#: inc/functions_pinterest.php:85 inc/functions_pinterest.php:185
#: inc/functions_pinterest.php:187 inc/meta.php:108 js/blocks-pinterest.js:154
msgid "Deprecated"
msgstr ""

#: inc/functions_pinterest.php:92 inc/functions_pinterest.php:187
#: js/blocks-pinterest.js:164
msgid "Pin Description"
msgstr ""

#: inc/functions_pinterest.php:98 inc/functions_pinterest.php:189
#: js/blocks-pinterest.js:173
msgid "Pin Repin ID"
msgstr ""

#: inc/functions_pinterest.php:103 inc/functions_pinterest.php:191
#: js/blocks-pinterest.js:182
msgid "Disable Pinning"
msgstr ""

#: inc/functions_pinterest.php:369
msgid "Pin"
msgstr ""

#: inc/functions_pinterest.php:561
msgid "Hidden Image"
msgstr ""

#: inc/license.php:74
msgid "License Key"
msgstr ""

#: inc/license.php:81
msgid "Save License"
msgstr ""

#: inc/license.php:85
msgid "Remove License"
msgstr ""

#: inc/license.php:88
msgid "Save or remove your license key."
msgstr ""

#: inc/license.php:114 inc/license.php:121
msgid "Activate License"
msgstr ""

#: inc/license.php:117
msgid "Deactivate License"
msgstr ""

#: inc/license.php:118
msgid "License is activated."
msgstr ""

#: inc/license.php:122
msgid "License is not activated."
msgstr ""

#: inc/license.php:125
msgid ""
"Unlimited License needed for use in a multisite environment. Please contact "
"support to upgrade."
msgstr ""

#: inc/license.php:134
msgid "License Status"
msgstr ""

#: inc/license.php:139
msgid "Renew Your License for Updates + Support!"
msgstr ""

#: inc/license.php:148
msgid "Licenses Used"
msgstr ""

#: inc/license.php:156
msgid "Expiration Date"
msgstr ""

#: inc/license.php:157
msgid "Lifetime"
msgstr ""

#: inc/meta.php:30
msgid "Details"
msgstr ""

#: inc/meta.php:31 inc/settings.php:2115
msgid "Share Count Recovery"
msgstr ""

#: inc/meta.php:57
msgid "Social Media Image"
msgstr ""

#: inc/meta.php:61
msgid "Upload an image for social media."
msgstr ""

#: inc/meta.php:64 inc/meta.php:99 inc/settings.php:2451
msgid "Select an Image"
msgstr ""

#: inc/meta.php:64 inc/meta.php:99 inc/settings.php:2451
msgid "Upload"
msgstr ""

#: inc/meta.php:70
msgid "Social Media Title"
msgstr ""

#: inc/meta.php:71
msgid "Write a custom title for social media."
msgstr ""

#: inc/meta.php:76
msgid "Social Media Description"
msgstr ""

#: inc/meta.php:77
msgid "Write a custom description for social media."
msgstr ""

#: inc/meta.php:92
msgid "Pinterest Image"
msgstr ""

#: inc/meta.php:96
msgid "Upload an image for Pinterest."
msgstr ""

#: inc/meta.php:108
msgid "Pinterest Title"
msgstr ""

#: inc/meta.php:109
msgid "Write a custom title for Pinterest."
msgstr ""

#: inc/meta.php:116
msgid "Pinterest Hidden Images"
msgstr ""

#: inc/meta.php:136
msgid "Select Images"
msgstr ""

#: inc/meta.php:136
msgid "Add Images"
msgstr ""

#: inc/meta.php:141
msgid "Pinterest Description"
msgstr ""

#: inc/meta.php:142
msgid "Write a custom description for Pinterest."
msgstr ""

#: inc/meta.php:157
msgid "Hide Inline Content"
msgstr ""

#: inc/meta.php:163
msgid "Hide Floating Bar"
msgstr ""

#: inc/meta.php:169
msgid "Disable Image Pins"
msgstr ""

#: inc/meta.php:175
msgid "Disable Highlight"
msgstr ""

#: inc/meta.php:188
msgid "Refresh Share Counts"
msgstr ""

#: inc/meta.php:194 inc/meta.php:233 inc/settings.php:2620
msgid "View Documentation"
msgstr ""

#: inc/meta.php:206
msgid ""
"Add previous URLs to recover social share counts. For example, changing a "
"slug on a URL."
msgstr ""

#: inc/meta.php:220 js/admin.js:360
msgid "Remove"
msgstr ""

#: inc/meta.php:230
msgid "Add URL"
msgstr ""

#: inc/meta.php:236
msgid "You've reached the maximum amount of recovery URLs."
msgstr ""

#: inc/network.php:35
msgid "Default settings applied!"
msgstr ""

#: inc/network.php:39
msgid "Select a site that is not already the Network Default."
msgstr ""

#: inc/network.php:44
msgid "Network Default not set."
msgstr ""

#: inc/network.php:50
msgid "Error: Blog Not Found."
msgstr ""

#: inc/network.php:57
msgid "Options saved."
msgstr ""

#: inc/network.php:70 inc/network.php:84
msgid "Apply Default Settings"
msgstr ""

#: inc/network.php:72
msgid ""
"Select a site from the dropdown and click to apply the settings from your "
"network default (above)."
msgstr ""

#: inc/network.php:77
msgid "Select a Site"
msgstr ""

#: inc/settings.php:18
msgid "Enable Inline Content"
msgstr ""

#: inc/settings.php:25
msgid ""
"Display social share buttons inline above or below your content. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:32 inc/settings.php:513 inc/settings.php:1066
#: inc/settings.php:1361
msgid "Social Networks"
msgstr ""

#: inc/settings.php:39
msgid ""
"Choose which inline social share buttons to display. Click on a square to "
"enable or disable that specific network. Drag and drop squares to arrange "
"the order in which they will display. Default: X, Facebook, LinkedIn"
msgstr ""

#: inc/settings.php:44 inc/settings.php:525 inc/settings.php:1374
#: js/blocks-share.js:599
msgid "Display"
msgstr ""

#: inc/settings.php:49 inc/settings.php:530 inc/settings.php:1379
#: inc/settings.php:1569
msgid "Post Types"
msgstr ""

#: inc/settings.php:56
msgid ""
"Choose which post types display inline social share buttons. Default: Posts"
msgstr ""

#: inc/settings.php:63
msgid "Show in Feeds"
msgstr ""

#: inc/settings.php:70
msgid "Show your inline social share buttons in post feeds. Default: Disabled"
msgstr ""

#: inc/settings.php:77 inc/settings.php:588 inc/settings.php:1606
msgid "Button Position"
msgstr ""

#: inc/settings.php:86
msgid "Above Content"
msgstr ""

#: inc/settings.php:87
msgid "Below Content"
msgstr ""

#: inc/settings.php:88
msgid "Above and Below Content"
msgstr ""

#: inc/settings.php:89 inc/settings.php:598
msgid "Don't Add to Content (shortcode)"
msgstr ""

#: inc/settings.php:91
msgid ""
"Choose where to display your inline social share buttons. Default: Above "
"Content"
msgstr ""

#: inc/settings.php:98 inc/settings.php:626 js/blocks-share.js:603
msgid "Mobile Breakpoint"
msgstr ""

#: inc/settings.php:109
msgid ""
"Set the width in pixels (px) where you want the inline mobile breakpoint to "
"occur. Default: 1200px"
msgstr ""

#: inc/settings.php:116 inc/settings.php:644 js/blocks-share.js:616
msgid "Hide Above Breakpoint"
msgstr ""

#: inc/settings.php:123
msgid ""
"Hide your inline social share buttons when the browser’s viewport is wider "
"than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:130 inc/settings.php:658 js/blocks-share.js:626
msgid "Hide Below Breakpoint"
msgstr ""

#: inc/settings.php:137
msgid ""
"Hide your inline social share buttons when the browser’s viewport is "
"narrower than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:142 inc/settings.php:758 inc/settings.php:1128
#: inc/settings.php:1391
msgid "Design"
msgstr ""

#: inc/settings.php:147 inc/settings.php:763 inc/settings.php:1133
#: js/blocks-share.js:397
msgid "Button Style"
msgstr ""

#: inc/settings.php:156 inc/settings.php:772 inc/settings.php:1142
#: js/blocks-share.js:405
msgid "Solid"
msgstr ""

#: inc/settings.php:157 inc/settings.php:773 inc/settings.php:1143
#: js/blocks-share.js:408
msgid "Inverse"
msgstr ""

#: inc/settings.php:158 inc/settings.php:1144 js/blocks-share.js:411
msgid "Bordered Label"
msgstr ""

#: inc/settings.php:159 inc/settings.php:1145 js/blocks-share.js:414
msgid "Bordered Button"
msgstr ""

#: inc/settings.php:160 inc/settings.php:1146 js/blocks-share.js:417
msgid "Minimal Label"
msgstr ""

#: inc/settings.php:161 inc/settings.php:1147 js/blocks-share.js:420
msgid "Minimal"
msgstr ""

#: inc/settings.php:163
msgid "Change the style of your inline social share buttons. Default: Solid"
msgstr ""

#: inc/settings.php:170 js/blocks-share.js:426
msgid "Button Layout"
msgstr ""

#: inc/settings.php:179 js/blocks-share.js:434
msgid "Auto Width"
msgstr ""

#: inc/settings.php:180 js/blocks-share.js:437
msgid "1 Column"
msgstr ""

#: inc/settings.php:181 js/blocks-share.js:440
msgid "2 Columns"
msgstr ""

#: inc/settings.php:182 js/blocks-share.js:443
msgid "3 Columns"
msgstr ""

#: inc/settings.php:183 js/blocks-share.js:446
msgid "4 Columns"
msgstr ""

#: inc/settings.php:184 js/blocks-share.js:449
msgid "5 Columns"
msgstr ""

#: inc/settings.php:185 js/blocks-share.js:452
msgid "6 Columns"
msgstr ""

#: inc/settings.php:187
msgid "Change the layout of your inline social share buttons."
msgstr ""

#: inc/settings.php:194 inc/settings.php:607 inc/widget.php:88
msgid "Button Alignment"
msgstr ""

#: inc/settings.php:203 inc/settings.php:616 inc/settings.php:1278
#: inc/widget.php:90 js/blocks.js:122 novashare.php:347
msgid "Left"
msgstr ""

#: inc/settings.php:204 inc/settings.php:617 inc/widget.php:91
msgid "Right"
msgstr ""

#: inc/settings.php:205 inc/settings.php:1619 inc/widget.php:92
msgid "Center"
msgstr ""

#: inc/settings.php:208
msgid "Choose how to align your inline social share buttons. Default: Left"
msgstr ""

#: inc/settings.php:215 inc/settings.php:782 inc/settings.php:1156
#: inc/widget.php:103 js/blocks-follow.js:206 js/blocks-share.js:495
msgid "Button Size"
msgstr ""

#: inc/settings.php:224 inc/settings.php:791 inc/settings.php:1165
#: js/blocks-share.js:505
msgid "Small"
msgstr ""

#: inc/settings.php:225 inc/settings.php:792 inc/settings.php:1166
#: inc/settings.php:1949 inc/settings.php:1956 js/blocks-share.js:512
msgid "Medium"
msgstr ""

#: inc/settings.php:226 inc/settings.php:793 inc/settings.php:1167
#: js/blocks-share.js:519
msgid "Large"
msgstr ""

#: inc/settings.php:228
msgid "Change the size of your inline social share buttons. Default: Medium"
msgstr ""

#: inc/settings.php:235 inc/settings.php:802 inc/settings.php:1176
#: inc/settings.php:1629 inc/widget.php:75 js/blocks-follow.js:143
#: js/blocks-share.js:525
msgid "Button Shape"
msgstr ""

#: inc/settings.php:244 inc/settings.php:811 inc/settings.php:1185
#: inc/settings.php:1405 inc/settings.php:1638 inc/widget.php:77
#: js/blocks-follow.js:153 js/blocks-share.js:535
msgid "Squared"
msgstr ""

#: inc/settings.php:245 inc/settings.php:812 inc/settings.php:1186
#: inc/settings.php:1406 inc/settings.php:1639 inc/widget.php:78
#: js/blocks-follow.js:160 js/blocks-share.js:542
msgid "Rounded"
msgstr ""

#: inc/settings.php:246 inc/settings.php:813 inc/settings.php:1187
#: inc/settings.php:1407 inc/settings.php:1640 inc/widget.php:79
#: js/blocks-follow.js:167 js/blocks-share.js:549
msgid "Circular"
msgstr ""

#: inc/settings.php:248
msgid "Change the shape of your inline social share buttons. Default: Squared"
msgstr ""

#: inc/settings.php:255 inc/settings.php:822 inc/settings.php:1416
#: inc/settings.php:1650 inc/widget.php:121 js/blocks-follow.js:258
#: js/blocks-follow.js:453 js/blocks-share.js:581
msgid "Button Color"
msgstr ""

#: inc/settings.php:263
msgid "Change the background color of your inline social share buttons."
msgstr ""

#: inc/settings.php:270 inc/settings.php:837 inc/settings.php:1431
#: inc/widget.php:127 js/blocks-follow.js:263 js/blocks-share.js:582
msgid "Button Hover Color"
msgstr ""

#: inc/settings.php:278
msgid "Change the hover background color of your inline social share buttons."
msgstr ""

#: inc/settings.php:285 inc/settings.php:852 inc/settings.php:1446
#: inc/settings.php:1666 inc/widget.php:133 js/blocks-follow.js:268
#: js/blocks-share.js:583
msgid "Icon Color"
msgstr ""

#: inc/settings.php:293
msgid "Change the icon color of your inline social share buttons."
msgstr ""

#: inc/settings.php:301 inc/settings.php:868 inc/settings.php:1462
#: inc/widget.php:139 js/blocks-follow.js:273 js/blocks-share.js:584
msgid "Icon Hover Color"
msgstr ""

#: inc/settings.php:309
msgid "Change the hover icon color of your inline social share buttons."
msgstr ""

#: inc/settings.php:317 inc/settings.php:884 inc/settings.php:1478
#: js/blocks-share.js:589
msgid "Inverse on Hover"
msgstr ""

#: inc/settings.php:324 inc/settings.php:891 inc/settings.php:1485
msgid ""
"Swap to an inverse button style on hover. This function will add additional "
"inline JavaScript to any page where Novashare buttons are present."
msgstr ""

#: inc/settings.php:332 inc/settings.php:899 inc/widget.php:112
#: js/blocks-follow.js:219 js/blocks-share.js:555
msgid "Button Margin"
msgstr ""

#: inc/settings.php:341
msgid ""
"Change the margin in pixels (px) around your inline social social share "
"buttons."
msgstr ""

#: inc/settings.php:348 js/blocks-follow.js:232 js/blocks-share.js:568
msgid "Show Labels"
msgstr ""

#: inc/settings.php:356
msgid ""
"Display network labels on your inline social share buttons. Default: Disabled"
msgstr ""

#: inc/settings.php:363
msgid "Hide Labels on Mobile"
msgstr ""

#: inc/settings.php:371
msgid ""
"Hide network labels on your inline social share buttons on mobile. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:376 inc/settings.php:993 inc/settings.php:1194
#: inc/settings.php:1919 js/blocks-share.js:635
msgid "Share Counts"
msgstr ""

#: inc/settings.php:381 inc/settings.php:998 inc/settings.php:1199
#: js/blocks-share.js:639
msgid "Total Share Count"
msgstr ""

#: inc/settings.php:389
msgid ""
"Display the total share count with your inline social share buttons. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:396 inc/settings.php:1013 js/blocks-share.js:652
msgid "Total Share Count Position"
msgstr ""

#: inc/settings.php:405 inc/settings.php:1022 js/blocks-share.js:662
msgid "Before"
msgstr ""

#: inc/settings.php:406 inc/settings.php:1023 js/blocks-share.js:669
msgid "After"
msgstr ""

#: inc/settings.php:409
msgid ""
"Change the position of your inline total share count display. Default: After"
msgstr ""

#: inc/settings.php:416 inc/settings.php:1033 js/blocks-share.js:676
msgid "Total Share Count Color"
msgstr ""

#: inc/settings.php:425
msgid "Change the text color of your inline total share count display."
msgstr ""

#: inc/settings.php:432 inc/settings.php:1049 inc/settings.php:1214
#: js/blocks-share.js:684
msgid "Network Share Counts"
msgstr ""

#: inc/settings.php:439 inc/settings.php:1056
msgid ""
"Display individual network share counts when hovering over each button. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:444 inc/settings.php:1078 js/blocks-share.js:694
msgid "Call to Action"
msgstr ""

#: inc/settings.php:449 inc/settings.php:1083 js/blocks-share.js:698
msgid "Text"
msgstr ""

#: inc/settings.php:458
msgid ""
"Set the call to action text displayed above your inline social share buttons."
msgstr ""

#: inc/settings.php:465 js/blocks-share.js:707
msgid "Font Size"
msgstr ""

#: inc/settings.php:474
msgid "Change the font size of your call to action text."
msgstr ""

#: inc/settings.php:481 inc/settings.php:1115 js/blocks-share.js:723
msgid "Font Color"
msgstr ""

#: inc/settings.php:489
msgid "Change the color of your call to action text."
msgstr ""

#: inc/settings.php:499
msgid "Enable Floating Bar"
msgstr ""

#: inc/settings.php:506
msgid "Display social share buttons on a floating bar. Default: Disabled"
msgstr ""

#: inc/settings.php:520
msgid ""
"Choose which floating social share buttons to display. Click on a square to "
"enable or disable that specific network. Drag and drop squares to arrange "
"the order in which they will display. Default: X, Facebook, LinkedIn"
msgstr ""

#: inc/settings.php:537
msgid ""
"Choose which post types display floating social share buttons. Default: Posts"
msgstr ""

#: inc/settings.php:544
msgid "Show on Posts Page"
msgstr ""

#: inc/settings.php:551
msgid ""
"Show your floating social share buttons on your posts page. Default: Disabled"
msgstr ""

#: inc/settings.php:558
msgid "Show on Archives"
msgstr ""

#: inc/settings.php:565
msgid ""
"Show your floating social share buttons on archive pages. Share counts are "
"not supported on archives. Default: Disabled"
msgstr ""

#: inc/settings.php:573
msgid "Show on Homepage"
msgstr ""

#: inc/settings.php:580
msgid ""
"Show your floating social share buttons on your homepage. Default: Disabled"
msgstr ""

#: inc/settings.php:597 inc/settings.php:1240 inc/settings.php:1757
msgid "Default"
msgstr ""

#: inc/settings.php:600
msgid "Choose where to display your floating social share buttons."
msgstr ""

#: inc/settings.php:619
msgid "Choose how to align your floating social share buttons. Default: Left"
msgstr ""

#: inc/settings.php:637
msgid ""
"Set the width in pixels (px) where you want the floating mobile breakpoint "
"to occur. Default: 1200px"
msgstr ""

#: inc/settings.php:651
msgid ""
"Hide your floating social share buttons when the browser’s viewport is wider "
"than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:666
msgid ""
"Hide your floating social share buttons when the browser’s viewport is "
"narrower than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:673
msgid "Top Offset"
msgstr ""

#: inc/settings.php:684
msgid ""
"Change the offset in pixels (px) or percentage (%) that your floating social "
"share button container will be from the top of the screen."
msgstr ""

#: inc/settings.php:691
msgid "Edge Offset"
msgstr ""

#: inc/settings.php:700
msgid ""
"Change the offset in pixels (px) that your floating social share button "
"container will be from the edge of the screen."
msgstr ""

#: inc/settings.php:707
msgid "Show on Scroll"
msgstr ""

#: inc/settings.php:715
msgid ""
"Wait to show your floating social share buttons until the user has scrolled "
"past a specific point on the page."
msgstr ""

#: inc/settings.php:722
msgid "Show on Scroll Location"
msgstr ""

#: inc/settings.php:732
msgid "Desktop Only"
msgstr ""

#: inc/settings.php:733
msgid "Mobile Only"
msgstr ""

#: inc/settings.php:736
msgid "Choose what devices are set to only show on scroll."
msgstr ""

#: inc/settings.php:743
msgid "Scroll Threshold"
msgstr ""

#: inc/settings.php:753
msgid "Change the point that show on scroll is triggered. px or %"
msgstr ""

#: inc/settings.php:775
msgid "Change the style of your floating social share buttons. Default: Solid"
msgstr ""

#: inc/settings.php:795
msgid "Change the size of your floating social share buttons. Default: Medium"
msgstr ""

#: inc/settings.php:815
msgid ""
"Change the shape of your floating social share buttons. Default: Squared"
msgstr ""

#: inc/settings.php:830
msgid "Change the background color of your floating social share buttons."
msgstr ""

#: inc/settings.php:845
msgid ""
"Change the hover background color of your floating social share buttons."
msgstr ""

#: inc/settings.php:860 inc/settings.php:876
msgid "Change the icon color of your floating social share buttons."
msgstr ""

#: inc/settings.php:908
msgid ""
"Change the margin in pixels (px) around your floating social social share "
"buttons."
msgstr ""

#: inc/settings.php:913
msgid "Mobile"
msgstr ""

#: inc/settings.php:918
msgid "Max Width"
msgstr ""

#: inc/settings.php:929
msgid ""
"Set the max width in pixels (px) up to where the floating social share "
"buttons should display on mobile."
msgstr ""

#: inc/settings.php:936 inc/settings.php:1100
msgid "Background Color"
msgstr ""

#: inc/settings.php:944
msgid ""
"Change the background color of the floating social share button container on "
"mobile."
msgstr ""

#: inc/settings.php:951
msgid "Background Padding"
msgstr ""

#: inc/settings.php:960
msgid "Change the padding around your floating social share buttons."
msgstr ""

#: inc/settings.php:967
msgid "Fill Available Space"
msgstr ""

#: inc/settings.php:974
msgid ""
"Allow your floating social share buttons to expand to fill the container."
msgstr ""

#: inc/settings.php:981
msgid "Hide Total Share Count"
msgstr ""

#: inc/settings.php:988
msgid ""
"Hide the total share count from your floating social share buttons on mobile."
msgstr ""

#: inc/settings.php:1006
msgid ""
"Display the total share count with your floating social share buttons. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:1026
msgid ""
"Change the position of your floating total share count display. Default: "
"After"
msgstr ""

#: inc/settings.php:1042
msgid "Change the text color of your floating bar total share count display."
msgstr ""

#: inc/settings.php:1073
msgid ""
"Choose which social share buttons to display when the share button is "
"clicked. Click on a square to enable or disable that specific network. Drag "
"and drop squares to arrange the order in which they will display. Default: "
"All Networks"
msgstr ""

#: inc/settings.php:1093
msgid "Change the call to action text displayed in your share button window."
msgstr ""

#: inc/settings.php:1108
msgid ""
"Change the background color of the call to action in your share button "
"window."
msgstr ""

#: inc/settings.php:1123
msgid ""
"Change the font color of the call to action in your share button window."
msgstr ""

#: inc/settings.php:1149
msgid ""
"Change the style of your share button window social share buttons. Default: "
"Solid"
msgstr ""

#: inc/settings.php:1169
msgid ""
"Change the size of your share button window social share buttons. Default: "
"Medium"
msgstr ""

#: inc/settings.php:1189
msgid ""
"Change the shape of your share button window social share buttons. Default: "
"Squared"
msgstr ""

#: inc/settings.php:1207
msgid "Display the total share count in your share button window."
msgstr ""

#: inc/settings.php:1221
msgid "Display individual network share counts when hovering over each button."
msgstr ""

#: inc/settings.php:1231 js/blocks.js:91 novashare.php:335
msgid "Theme"
msgstr ""

#: inc/settings.php:1241
msgid "Simple"
msgstr ""

#: inc/settings.php:1242
msgid "Simple Alternate"
msgstr ""

#: inc/settings.php:1244
msgid "Change the visual style of your click to post boxes."
msgstr ""

#: inc/settings.php:1251 js/blocks.js:107 novashare.php:342
msgid "Call to Action Text"
msgstr ""

#: inc/settings.php:1261
msgid ""
"Change the default call to action text displayed on your click to post boxes."
msgstr ""

#: inc/settings.php:1268 js/blocks.js:117 novashare.php:344
msgid "Call to Action Position"
msgstr ""

#: inc/settings.php:1277 js/blocks.js:121 novashare.php:346
msgid "Right (Default)"
msgstr ""

#: inc/settings.php:1280
msgid ""
"Change the position of your call to action text displayed on your click to "
"post boxes."
msgstr ""

#: inc/settings.php:1287 js/blocks.js:132
msgid "Remove URL"
msgstr ""

#: inc/settings.php:1294
msgid ""
"Change the default status of the remove url option on your click to post "
"boxes."
msgstr ""

#: inc/settings.php:1301 js/blocks.js:142 novashare.php:355
msgid "Remove Username"
msgstr ""

#: inc/settings.php:1308
msgid ""
"Change the default status of the remove username option on your click to "
"post boxes."
msgstr ""

#: inc/settings.php:1315 js/blocks.js:152 novashare.php:359
msgid "Hide Hashtags"
msgstr ""

#: inc/settings.php:1322
msgid ""
"Change the default status of the hide hashtags option on your click to post "
"boxes."
msgstr ""

#: inc/settings.php:1329 js/blocks.js:162 novashare.php:362
msgid "Accent Color"
msgstr ""

#: inc/settings.php:1337
msgid "Change the default accent color used on your click to post boxes."
msgstr ""

#: inc/settings.php:1347
msgid "Enable Highlight"
msgstr ""

#: inc/settings.php:1354
msgid "Display social share buttons over highlighted text."
msgstr ""

#: inc/settings.php:1369
msgid ""
"Choose which highlight social share buttons to display. Click on a square to "
"enable or disable that specific network. Drag and drop squares to arrange "
"the order in which they will display."
msgstr ""

#: inc/settings.php:1386
msgid "Choose which post types display highlight social share buttons."
msgstr ""

#: inc/settings.php:1396
msgid "Container Shape"
msgstr ""

#: inc/settings.php:1409
msgid "Change the shape of your highlight social share button container."
msgstr ""

#: inc/settings.php:1424
msgid "Change the background color of your highlight social share buttons."
msgstr ""

#: inc/settings.php:1439
msgid ""
"Change the hover background color of your highlight social share buttons."
msgstr ""

#: inc/settings.php:1454 inc/settings.php:1470
msgid "Change the icon color of your highlight social share buttons."
msgstr ""

#: inc/settings.php:1496
msgid "Share Button Behavior"
msgstr ""

#: inc/settings.php:1505 inc/settings.php:1595
msgid "Share Post Image"
msgstr ""

#: inc/settings.php:1506 inc/settings.php:1596
msgid "Show Image Grid"
msgstr ""

#: inc/settings.php:1509
msgid ""
"Change what happens when the Pinterest share button is clicked. Default: "
"Share Post Image"
msgstr ""

#: inc/settings.php:1516
msgid "Image Grid Behavior"
msgstr ""

#: inc/settings.php:1525
msgid "Show All Pinnable Images"
msgstr ""

#: inc/settings.php:1526
msgid "Show Only Pinterest Images"
msgstr ""

#: inc/settings.php:1530
msgid "Choose which images display in the image grid."
msgstr ""

#: inc/settings.php:1537
msgid "Enable Image Attributes"
msgstr ""

#: inc/settings.php:1545
msgid ""
"Allow for control of Pinterest-specific image attributes throughout the "
"plugin, even if image pins are not enabled. Default: Disabled"
msgstr ""

#: inc/settings.php:1549
msgid "Image Pins"
msgstr ""

#: inc/settings.php:1554
msgid "Enable Image Pins"
msgstr ""

#: inc/settings.php:1562
msgid ""
"Show Pinterest pin buttons when hovering over images in your content. "
"Default: Disabled"
msgstr ""

#: inc/settings.php:1577
msgid "Choose which post types display Pinterest image pins."
msgstr ""

#: inc/settings.php:1584
msgid "Button Behavior"
msgstr ""

#: inc/settings.php:1594
msgid "Share Pin Image"
msgstr ""

#: inc/settings.php:1599
msgid ""
"Change what happens when a Pinterest image pin button is clicked. Default: "
"Share Pin Image"
msgstr ""

#: inc/settings.php:1615
msgid "Top Left"
msgstr ""

#: inc/settings.php:1616
msgid "Top Right"
msgstr ""

#: inc/settings.php:1617
msgid "Bottom Left"
msgstr ""

#: inc/settings.php:1618
msgid "Bottom Right"
msgstr ""

#: inc/settings.php:1622
msgid ""
"Choose where on the image to display your Pinterest image pin buttons. "
"Default: Top Left"
msgstr ""

#: inc/settings.php:1643
msgid "Change the shape of your Pinterest image pin buttons. Default: Squared"
msgstr ""

#: inc/settings.php:1659
msgid "Change the background color of your Pinterest image pin buttons."
msgstr ""

#: inc/settings.php:1675
msgid "Change the icon color of your Pinterest image pin buttons."
msgstr ""

#: inc/settings.php:1682
msgid "Always Show"
msgstr ""

#: inc/settings.php:1691
msgid ""
"Always show Pinterest image pin buttons instead of only when hovering over "
"an image. Default: Disabled"
msgstr ""

#: inc/settings.php:1698
msgid "Hide Button Labels"
msgstr ""

#: inc/settings.php:1707
msgid "Hide the labels on your Pinterest image pin buttons. Default: Disabled"
msgstr ""

#: inc/settings.php:1714
msgid "Excluded Images"
msgstr ""

#: inc/settings.php:1725
msgid ""
"Exclude specific images from getting pins applied. Exclude an image by "
"adding the source URL (example.png) or by adding any unique portion of its "
"attribute string (class=\"example\"). Format: one per line"
msgstr ""

#: inc/settings.php:1735
msgid "Enable X Counts"
msgstr ""

#: inc/settings.php:1742
msgid ""
"Request and store X share counts using a third party service. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:1749
msgid "X Count Service"
msgstr ""

#: inc/settings.php:1761
msgid "Choose which service to use to pull X share counts."
msgstr ""

#: inc/settings.php:1768
msgid "X Username"
msgstr ""

#: inc/settings.php:1777 inc/settings.php:1840
msgid "novashare"
msgstr ""

#: inc/settings.php:1778
msgid "The username used when sharing content to X."
msgstr ""

#: inc/settings.php:1785
msgid "Facebook App ID"
msgstr ""

#: inc/settings.php:1793
msgid "The Facebook App ID from your Facebook Developer Application."
msgstr ""

#: inc/settings.php:1800
msgid "Facebook App Secret"
msgstr ""

#: inc/settings.php:1808
msgid "The Facebook App Secret from your Facebook Developer Application."
msgstr ""

#: inc/settings.php:1815
msgid "Subscribe Link"
msgstr ""

#: inc/settings.php:1824
msgid "The URL used for your subscribe button."
msgstr ""

#: inc/settings.php:1831
msgid "Mastodon Username"
msgstr ""

#: inc/settings.php:1841
msgid "The username used when sharing content to Mastodon."
msgstr ""

#: inc/settings.php:1848
msgid "Custom CSS"
msgstr ""

#: inc/settings.php:1856
msgid "Add custom CSS to only load when needed."
msgstr ""

#: inc/settings.php:1861
msgid "Meta"
msgstr ""

#: inc/settings.php:1867
msgid ""
"A compatible SEO plugin is active. Meta and open graph data that is not "
"specific to Novashare will be handled there."
msgstr ""

#: inc/settings.php:1876
msgid "Enable Open Graph"
msgstr ""

#. translators: s. <head> tag, do not translate
#: inc/settings.php:1884
#, php-format
msgid ""
"Print out open graph meta tags with Novashare in the %s section of your "
"site. Default: Enabled"
msgstr ""

#: inc/settings.php:1893
msgid "Hide Meta Box"
msgstr ""

#: inc/settings.php:1899
msgid "Hide Novashare meta box in the WordPress editor. Default: Disabled"
msgstr ""

#: inc/settings.php:1906
msgid "Default Social Image"
msgstr ""

#: inc/settings.php:1913
msgid ""
"Add a default image that will be used for share links and meta tags if no "
"post specific images are found."
msgstr ""

#: inc/settings.php:1924
msgid "Minimum Share Count"
msgstr ""

#: inc/settings.php:1933
msgid ""
"Set a minimum total share count threshold to reach before share counts are "
"displayed."
msgstr ""

#: inc/settings.php:1940
msgid "Share Counts Refresh Rate"
msgstr ""

#: inc/settings.php:1948 inc/settings.php:1955
msgid "High"
msgstr ""

#: inc/settings.php:1950 inc/settings.php:1957
msgid "Low"
msgstr ""

#: inc/settings.php:1952
msgid ""
"Adjust the rate at which your social share counts are refreshed based on the "
"modified date. Default: High"
msgstr ""

#: inc/settings.php:1954
msgid "Modified"
msgstr ""

#: inc/settings.php:1954
msgid "days"
msgstr ""

#: inc/settings.php:1955 inc/settings.php:1956 inc/settings.php:1957
msgid "hours"
msgstr ""

#: inc/settings.php:1965 inc/settings.php:1973
msgid "Purge Share Counts"
msgstr ""

#: inc/settings.php:1974
msgid ""
"Are you sure? This will delete all existing share count data for all posts "
"from the database."
msgstr ""

#: inc/settings.php:1975
msgid "Permanently delete all existing share counts from your database."
msgstr ""

#: inc/settings.php:1980
msgid "Google Analytics"
msgstr ""

#: inc/settings.php:1985
msgid "Enable UTM Tracking"
msgstr ""

#: inc/settings.php:1992
msgid "Add UTM parameters to social sharing links."
msgstr ""

#: inc/settings.php:1999
msgid "Campaign UTM Source"
msgstr ""

#: inc/settings.php:2009
msgid ""
"The value of the UTM source parameter added to your social sharing links. "
"Use {{network}} to dynamically populate the value with the relative social "
"network. Default: {{network}}"
msgstr ""

#: inc/settings.php:2016
msgid "Campaign UTM Medium"
msgstr ""

#: inc/settings.php:2026
msgid ""
"The value of the UTM medium parameter added to your social sharing links. "
"Default: social"
msgstr ""

#: inc/settings.php:2033
msgid "Campaign UTM Name"
msgstr ""

#: inc/settings.php:2043
msgid ""
"The value of the UTM name parameter added to your social sharing links. "
"Default: novashare"
msgstr ""

#: inc/settings.php:2048
msgid "Link Shortening"
msgstr ""

#: inc/settings.php:2053
msgid "Enable Bitly"
msgstr ""

#: inc/settings.php:2060
msgid "Generate Bitly short links for all share URLs."
msgstr ""

#: inc/settings.php:2067
msgid "Generic Access Token"
msgstr ""

#: inc/settings.php:2076
msgid "The Generic Access Token from your Bitly account."
msgstr ""

#: inc/settings.php:2083
msgid "Group"
msgstr ""

#: inc/settings.php:2092
msgid ""
"The group from your Bitly account used to generate and store short links. "
"Non-enterprise users will only have one default group."
msgstr ""

#: inc/settings.php:2099 inc/settings.php:2107
msgid "Purge Short Links"
msgstr ""

#: inc/settings.php:2109
msgid ""
"Are you sure? This will delete all existing short links from the database."
msgstr ""

#: inc/settings.php:2110
msgid "Permanently delete all existing short links from your database."
msgstr ""

#: inc/settings.php:2120
msgid "Combine HTTP & HTTPS"
msgstr ""

#: inc/settings.php:2126
msgid ""
"Combine share counts for HTTP and HTTPS URLs for networks that store them "
"separately. This will double the amount of API calls for those networks."
msgstr ""

#: inc/settings.php:2133
msgid "Recover Previous Permalinks"
msgstr ""

#: inc/settings.php:2140
msgid "Recover share counts for a previous permalink structure."
msgstr ""

#: inc/settings.php:2147
msgid "Previous Permalink Structure"
msgstr ""

#: inc/settings.php:2155
msgid "Select a Structure"
msgstr ""

#: inc/settings.php:2156
msgid "Plain"
msgstr ""

#: inc/settings.php:2157
msgid "Day and name"
msgstr ""

#: inc/settings.php:2158
msgid "Month and name"
msgstr ""

#: inc/settings.php:2159
msgid "Numeric"
msgstr ""

#: inc/settings.php:2160
msgid "Post name"
msgstr ""

#: inc/settings.php:2161
msgid "Custom Structure"
msgstr ""

#: inc/settings.php:2164
msgid "The permalink structure used to recover share counts."
msgstr ""

#: inc/settings.php:2171
msgid "Custom Permalink Structure"
msgstr ""

#: inc/settings.php:2179
msgid ""
"If you are recovering share counts for a custom permalink structure, please "
"provide that structure here."
msgstr ""

#: inc/settings.php:2187
msgid "Recover Previous Domain"
msgstr ""

#: inc/settings.php:2194
msgid "Recover share counts for a previous domain."
msgstr ""

#: inc/settings.php:2201
msgid "Previous Domain"
msgstr ""

#: inc/settings.php:2210
msgid "The domain used to recover share counts."
msgstr ""

#: inc/settings.php:2231
msgid ""
"Permanently delete all Novashare data from your database when the plugin is "
"uninstalled."
msgstr ""

#: inc/settings.php:2239
msgid "Accessibility Mode"
msgstr ""

#: inc/settings.php:2247
msgid ""
"Disable the use of visual UI elements in the plugin settings such as "
"checkbox toggles and hovering tooltips."
msgstr ""

#: inc/settings.php:2254 inc/settings.php:2262
msgid "Restore Default Options"
msgstr ""

#: inc/settings.php:2263
msgid ""
"Are you sure? This will remove all existing plugin options and restore them "
"to their default states."
msgstr ""

#: inc/settings.php:2265
msgid "Restore all plugin options to their default settings."
msgstr ""

#: inc/settings.php:2272
msgid "Export Settings"
msgstr ""

#: inc/settings.php:2280
msgid "Export Plugin Settings"
msgstr ""

#: inc/settings.php:2282
msgid ""
"Export your Novashare settings for this site as a .json file. This lets you "
"easily import the configuration into another site."
msgstr ""

#: inc/settings.php:2289
msgid "Import Settings"
msgstr ""

#: inc/settings.php:2294
msgid "Import Novashare settings from an exported .json file."
msgstr ""

#: inc/settings.php:2448
msgid "Upload an image."
msgstr ""

#: inc/settings.php:2568
msgid "Import Plugin Settings"
msgstr ""

#. translators: s. tooltip icon html, do not translate
#: inc/settings.php:2596
#, php-format
msgid "Click %s to view documentation."
msgstr ""

#: inc/support.php:9 inc/support.php:12
msgid "Documentation"
msgstr ""

#: inc/support.php:11
msgid ""
"Need help? Check out our in-depth documentation. Every feature has a step-by-"
"step walkthrough."
msgstr ""

#: inc/support.php:18 inc/support.php:21
msgid "Contact Us"
msgstr ""

#: inc/support.php:20
msgid ""
"If you have questions or problems, please send us a message. We’ll get back "
"to you as soon as possible."
msgstr ""

#: inc/support.php:27
msgid "Frequently Asked Questions"
msgstr ""

#: inc/support.php:31
msgid "How do I license activate the plugin?"
msgstr ""

#: inc/support.php:32
msgid "How do I update the plugin?"
msgstr ""

#: inc/support.php:33
msgid "How do I upgrade my license?"
msgstr ""

#: inc/support.php:34
msgid "Where can I view the changelog?"
msgstr ""

#: inc/support.php:35
msgid "Where can I sign up for the affiliate program?"
msgstr ""

#: inc/support.php:38
msgid "How do I enable inline share buttons?"
msgstr ""

#: inc/support.php:39
msgid "How do I enable floating bar share buttons?"
msgstr ""

#: inc/support.php:40
msgid "How do I enable share counts (total + network)?"
msgstr ""

#: inc/support.php:41
msgid "How do I customize Click to Tweet settings?"
msgstr ""

#: inc/support.php:42
msgid "How do I enable Pinterest image hover pins?"
msgstr ""

#: inc/widget.php:8
msgid "Follow Widget"
msgstr ""

#: inc/widget.php:9
msgid "Add Novashare follow buttons for your social network profiles."
msgstr ""

#: inc/widget.php:67
msgid "Title"
msgstr ""

#: inc/widget.php:146 js/blocks-follow.js:242
msgid "Open Links in New Tab"
msgstr ""

#: inc/widget.php:175
msgid "Select a Network"
msgstr ""

#: inc/widget.php:194
msgid "Add Network"
msgstr ""

#: inc/widget.php:266
msgid "That network is already in use."
msgstr ""

#: inc/widget.php:281
msgid "Page Name"
msgstr ""

#: inc/widget.php:284
msgid "Username"
msgstr ""

#: inc/widget.php:463
msgid "Send Email"
msgstr ""

#: inc/widget.php:467
msgid "Call Phone"
msgstr ""

#: inc/widget.php:487
msgid "Follow on"
msgstr ""

#: js/admin.js:435
msgid "Characters"
msgstr ""

#: js/admin.js:438
msgid "Character"
msgstr ""

#: js/admin.js:442
msgid "Remaining"
msgstr ""

#: js/blocks-follow.js:37 js/blocks-follow.js:123
msgid "Follow Buttons"
msgstr ""

#: js/blocks-follow.js:38
msgid "Add follow buttons for your social network profiles."
msgstr ""

#: js/blocks-follow.js:126 js/blocks-share.js:331
msgid "Add Networks"
msgstr ""

#: js/blocks-follow.js:139 js/blocks-follow.js:432 js/blocks-share.js:393
#: js/blocks.js:87 novashare.php:402
msgid "Settings"
msgstr ""

#: js/blocks-follow.js:173 js/blocks-share.js:460
msgid "Alignment"
msgstr ""

#: js/blocks-follow.js:253 js/blocks-follow.js:448 js/blocks-share.js:578
msgid "Colors"
msgstr ""

#: js/blocks-follow.js:338 js/blocks-share.js:775
msgid "Follow Network"
msgstr ""

#: js/blocks-follow.js:339 js/blocks-share.js:776
msgid "Add an icon linking to a social network profile."
msgstr ""

#: js/blocks-follow.js:412
msgid "Apply"
msgstr ""

#: js/blocks-follow.js:436
msgid "Icon SVG HTML"
msgstr ""

#: js/blocks-pinterest.js:190
msgid "Pin Image"
msgstr ""

#: js/blocks-pinterest.js:200
msgid "Upload Image"
msgstr ""

#: js/blocks-pinterest.js:216
msgid "Replace Image"
msgstr ""

#: js/blocks-pinterest.js:222
msgid "Remove image"
msgstr ""

#: js/blocks-share.js:102 js/blocks-share.js:327
msgid "Share Buttons"
msgstr ""

#: js/blocks-share.js:103
msgid "Add share buttons for your social network profiles."
msgstr ""

#: js/blocks-share.js:673 js/blocks-share.js:720
msgid "Color"
msgstr ""

#: js/blocks.js:33
msgid "Add an X post box."
msgstr ""

#: js/blocks.js:94 js/blocks.js:120
msgid "Global"
msgstr ""

#: js/blocks.js:95
msgid "Default (Accent Background)"
msgstr ""

#: js/blocks.js:96 novashare.php:338
msgid "Simple (Transparent Background)"
msgstr ""

#: js/blocks.js:97 novashare.php:339
msgid "Simple Alternate (Gray Background)"
msgstr ""

#: js/blocks.js:285 novashare.php:363
msgid "Characters Remaining"
msgstr ""

#: novashare.php:100
msgid "Action failed."
msgstr ""

#: novashare.php:330
msgid "Click to Post Shortcode"
msgstr ""

#: novashare.php:331
msgid "Insert Shortcode"
msgstr ""

#: novashare.php:333
msgid "Post"
msgstr ""

#: novashare.php:337
msgid "Default (Black Background)"
msgstr ""

#: novashare.php:351
msgid "Remove Post URL"
msgstr ""

#: novashare.php:352
msgid "The current URL will not be added to the post."
msgstr ""

#: novashare.php:356
msgid "The X username saved in Novashare will not be added to the post."
msgstr ""

#: novashare.php:360
msgid "Trailing hashtags will be hidden from the display box."
msgstr ""

#: novashare.php:428
msgid "Enter valid license key for automatic updates."
msgstr ""

#. Plugin Name of the plugin/theme
msgid "Novashare"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://novashare.io/"
msgstr ""

#. Description of the plugin/theme
msgid "Novashare is a lightweight and fast social media sharing plugin."
msgstr ""

#. Author of the plugin/theme
msgid "forgemedia"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://forgemedia.io/"
msgstr ""
