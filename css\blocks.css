/* click to tweet block */
body div.ns-ctt {
	margin-bottom: 3px;
}
body div.ns-ctt div[role=textbox] {
	pointer-events: auto;
}

/* character count */
.ns-ctt-char-count {
	font-size: 14px;
	font-style: italic;
}
.ns-ctt-positive {
	color: #aaaaaa;
}
.ns-ctt-negative {
	color: #ff3838;
}

/* block settings sidebar panel */
.novashare-block-settings select {
	max-width: none;
}

/* nsColorPicker */
.ns-color-picker:first-of-type {
	border-top: 1px solid #e0e0e0;
}
.ns-color-picker {
	border-left: 1px solid #e0e0e0;
	border-right: 1px solid #e0e0e0;
	border-bottom: 1px solid #e0e0e0;
}
.ns-color-popover legend {
	display: none;
}
.ns-buttons.is-selected .components-button.block-list-appender__toggle {
	display: inline-flex;
}