msgid ""
msgstr ""
"Project-Id-Version: Novashare\n"
"POT-Creation-Date: 2022-05-02 14:05-0700\n"
"PO-Revision-Date: 2022-06-10 12:28+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: novashare.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#. translators: the plugin name.
#: inc/Novashare_Plugin_Updater.php:224
#, php-format
msgid "There is a new version of %1$s available."
msgstr ""

#: inc/Novashare_Plugin_Updater.php:230
msgid "Contact your network administrator to install the update."
msgstr ""

#. translators: 1. opening anchor tag, do not translate 2. the new plugin version 3. closing anchor tag, do not translate.
#: inc/Novashare_Plugin_Updater.php:235
#, php-format
msgid "%1$sView version %2$s details%3$s."
msgstr ""

#: inc/Novashare_Plugin_Updater.php:243
#, php-format
msgid "%1$sView version %2$s details%3$s or %4$supdate now%5$s."
msgstr ""

#: inc/Novashare_Plugin_Updater.php:254
msgid "Update now."
msgstr ""

#: inc/Novashare_Plugin_Updater.php:486
msgid "You do not have permission to install plugin updates"
msgstr ""

#: inc/Novashare_Plugin_Updater.php:486
msgid "Error"
msgstr ""

#: inc/admin.php:22 inc/settings.php:13
msgid "Inline Content"
msgstr ""

#: inc/admin.php:23 inc/settings.php:447
msgid "Floating Bar"
msgstr ""

#: inc/admin.php:24 inc/settings.php:981
msgid "Share Button"
msgstr ""

#: inc/admin.php:25 inc/click_to_tweet.php:133 inc/settings.php:1114
#: inc/settings.php:1148 js/blocks.js:31 novashare.php:262
msgid "Click to Tweet"
msgstr ""

#: inc/admin.php:26 inc/settings.php:1230
msgid "Pinterest"
msgstr ""

#: inc/admin.php:27 inc/settings.php:1362
msgid "Configuration"
msgstr ""

#: inc/functions.php:238
msgid "Subscribe"
msgstr ""

#: inc/functions.php:1103
msgid "URL Copied"
msgstr ""

#: inc/functions.php:1152
msgid "SHARES"
msgstr "PARTAGES"

#: inc/functions.php:1152
msgid "SHARE"
msgstr "PARTAGE"

#: inc/functions.php:1187 inc/settings.php:1012
msgid "Share to..."
msgstr ""

#: inc/functions.php:1407
msgid "Share counts purged."
msgstr ""

#: inc/functions.php:1410
msgid "Share counts not purged."
msgstr ""

#: inc/functions.php:1427
msgid "Short links purged."
msgstr ""

#: inc/functions.php:1430
msgid "Short links not purged."
msgstr ""

#: inc/functions.php:1449
msgid "Successfully restored default options."
msgstr ""

#: inc/functions.php:1481
msgid "No import file given."
msgstr ""

#: inc/functions.php:1489
msgid "Please upload a valid .json file."
msgstr ""

#: inc/functions.php:1504
msgid "Successfully imported Novashare settings."
msgstr ""

#: inc/functions_pinterest.php:69 inc/functions_pinterest.php:167
#: js/blocks-pinterest.js:149
msgid "Pin Title"
msgstr ""

#: inc/functions_pinterest.php:75 inc/functions_pinterest.php:169
#: js/blocks-pinterest.js:158
msgid "Pin Description"
msgstr ""

#: inc/functions_pinterest.php:76 inc/functions_pinterest.php:169
#: inc/meta.php:109 js/blocks-pinterest.js:159
msgid ""
"Pinterest does not yet support passing both a title and description from a "
"pin. We've added both fields in advance, but currently, only the title will "
"be sent to Pinterest."
msgstr ""

#: inc/functions_pinterest.php:82 inc/functions_pinterest.php:171
#: js/blocks-pinterest.js:168
msgid "Pin Repin ID"
msgstr ""

#: inc/functions_pinterest.php:87 inc/functions_pinterest.php:173
#: js/blocks-pinterest.js:177
msgid "Disable Pinning"
msgstr ""

#: inc/functions_pinterest.php:320
msgid "Pin"
msgstr ""

#: inc/license.php:67 novashare.php:374
msgid "License"
msgstr ""

#: inc/license.php:74
msgid "License Key"
msgstr ""

#: inc/license.php:80
msgid "Save License"
msgstr ""

#: inc/license.php:84
msgid "Remove License"
msgstr ""

#: inc/license.php:87
msgid "Save or remove your license key."
msgstr ""

#: inc/license.php:113 inc/license.php:120
msgid "Activate License"
msgstr ""

#: inc/license.php:116
msgid "Deactivate License"
msgstr ""

#: inc/license.php:117
msgid "License is activated."
msgstr ""

#: inc/license.php:121
msgid "License is not activated."
msgstr ""

#: inc/license.php:124
msgid ""
"Unlimited License needed for use in a multisite environment. Please contact "
"support to upgrade."
msgstr ""

#: inc/license.php:133
msgid "Customer Email"
msgstr ""

#: inc/license.php:141
msgid "License Status"
msgstr ""

#: inc/license.php:146
msgid "Renew Your License for Updates + Support!"
msgstr ""

#: inc/license.php:155
msgid "Licenses Used"
msgstr ""

#: inc/license.php:163
msgid "Expiration Date"
msgstr ""

#: inc/license.php:164
msgid "Lifetime"
msgstr ""

#: inc/meta.php:26
msgid "Details"
msgstr ""

#: inc/meta.php:27 inc/settings.php:1724
msgid "Share Count Recovery"
msgstr ""

#: inc/meta.php:53
msgid "Social Media Image"
msgstr ""

#: inc/meta.php:57
msgid "Upload an image for social media."
msgstr ""

#: inc/meta.php:60 inc/meta.php:95 inc/settings.php:2063
msgid "Select an Image"
msgstr ""

#: inc/meta.php:60 inc/meta.php:95 inc/settings.php:2063
msgid "Upload"
msgstr ""

#: inc/meta.php:66
msgid "Social Media Title"
msgstr ""

#: inc/meta.php:67
msgid "Write a custom title for social media."
msgstr ""

#: inc/meta.php:72
msgid "Social Media Description"
msgstr ""

#: inc/meta.php:73
msgid "Write a custom description for social media."
msgstr ""

#: inc/meta.php:88
msgid "Pinterest Image"
msgstr ""

#: inc/meta.php:92
msgid "Upload an image for Pinterest."
msgstr ""

#: inc/meta.php:101
msgid "Pinterest Title"
msgstr ""

#: inc/meta.php:102
msgid "Write a custom title for Pinterest."
msgstr ""

#: inc/meta.php:107
msgid "Pinterest Description"
msgstr ""

#: inc/meta.php:108
msgid "Write a custom description for Pinterest."
msgstr ""

#: inc/meta.php:124
msgid "Hide Inline Content"
msgstr ""

#: inc/meta.php:130
msgid "Hide Floating Bar"
msgstr ""

#: inc/meta.php:136
msgid "Disable Image Pins"
msgstr ""

#: inc/meta.php:150
msgid "Refresh Share Counts"
msgstr ""

#: inc/meta.php:156 inc/meta.php:195 inc/settings.php:2226
msgid "View Documentation"
msgstr ""

#: inc/meta.php:168
msgid ""
"Add previous URLs to recover social share counts. For example, changing a "
"slug on a URL."
msgstr ""

#: inc/meta.php:182 js/admin.js:240
msgid "Remove"
msgstr ""

#: inc/meta.php:192
msgid "Add URL"
msgstr ""

#: inc/meta.php:198
msgid "You've reached the maximum amount of recovery URLs."
msgstr ""

#: inc/network.php:17
msgid "Network Access"
msgstr ""

#: inc/network.php:22
msgid "Choose who has access to manage Novashare plugin settings."
msgstr ""

#: inc/network.php:29
msgid "Network Default"
msgstr ""

#: inc/network.php:34
msgid "Choose a subsite that you want to pull default settings from."
msgstr ""

#: inc/network.php:41 inc/settings.php:1833
msgid "Clean Uninstall"
msgstr ""

#: inc/network.php:48
msgid ""
"When enabled, this will cause all Novashare options data to be removed from "
"your database when the plugin is uninstalled."
msgstr ""

#: inc/network.php:61
msgid "Site Admins (Default)"
msgstr ""

#: inc/network.php:62
msgid "Super Admins Only"
msgstr ""

#: inc/network.php:78
msgid "None"
msgstr ""

#: inc/network.php:126
msgid "Default settings applied!"
msgstr ""

#: inc/network.php:130
msgid "Select a site that is not already the Network Default."
msgstr ""

#: inc/network.php:135
msgid "Network Default not set."
msgstr ""

#: inc/network.php:141
msgid "Error: Blog Not Found."
msgstr ""

#: inc/network.php:148
msgid "Options saved."
msgstr ""

#: inc/network.php:172 inc/network.php:186
msgid "Apply Default Settings"
msgstr ""

#: inc/network.php:174
msgid ""
"Select a site from the dropdown and click to apply the settings from your "
"network default (above)."
msgstr ""

#: inc/network.php:179
msgid "Select a Site"
msgstr ""

#: inc/settings.php:18
msgid "Enable Inline Content"
msgstr ""

#: inc/settings.php:25
msgid ""
"Display social share buttons inline above or below your content. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:32 inc/settings.php:466 inc/settings.php:986
msgid "Social Networks"
msgstr ""

#: inc/settings.php:39
msgid ""
"Choose which inline social share buttons to display. Click on a square to "
"enable or disable that specific network. Drag and drop squares to arrange "
"the order in which they will display. Default: Twitter, Facebook, LinkedIn"
msgstr ""

#: inc/settings.php:44 inc/settings.php:478
msgid "Display"
msgstr ""

#: inc/settings.php:49 inc/settings.php:483 inc/settings.php:1271
msgid "Post Types"
msgstr ""

#: inc/settings.php:56
msgid ""
"Choose which post types display inline social share buttons. Default: Posts"
msgstr ""

#: inc/settings.php:63 inc/settings.php:541 inc/settings.php:1286
msgid "Button Position"
msgstr ""

#: inc/settings.php:72
msgid "Above Content"
msgstr ""

#: inc/settings.php:73
msgid "Below Content"
msgstr ""

#: inc/settings.php:74
msgid "Above and Below Content"
msgstr ""

#: inc/settings.php:75 inc/settings.php:551
msgid "Don't Add to Content (shortcode)"
msgstr ""

#: inc/settings.php:77
msgid ""
"Choose where to display your inline social share buttons. Default: Above "
"Content"
msgstr ""

#: inc/settings.php:84 inc/settings.php:579
msgid "Mobile Breakpoint"
msgstr ""

#: inc/settings.php:95
msgid ""
"Set the width in pixels (px) where you want the inline mobile breakpoint to "
"occur. Default: 1200px"
msgstr ""

#: inc/settings.php:102 inc/settings.php:597
msgid "Hide Above Breakpoint"
msgstr ""

#: inc/settings.php:109
msgid ""
"Hide your inline social share buttons when the browser’s viewport is wider "
"than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:116 inc/settings.php:611
msgid "Hide Below Breakpoint"
msgstr ""

#: inc/settings.php:123
msgid ""
"Hide your inline social share buttons when the browser’s viewport is "
"narrower than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:128 inc/settings.php:711 inc/settings.php:1048
msgid "Design"
msgstr ""

#: inc/settings.php:133 inc/settings.php:716 inc/settings.php:1053
msgid "Button Style"
msgstr ""

#: inc/settings.php:142 inc/settings.php:725 inc/settings.php:1062
msgid "Solid"
msgstr ""

#: inc/settings.php:143 inc/settings.php:726 inc/settings.php:1063
msgid "Inverse"
msgstr ""

#: inc/settings.php:144 inc/settings.php:1064
msgid "Bordered Label"
msgstr ""

#: inc/settings.php:145 inc/settings.php:1065
msgid "Bordered Button"
msgstr ""

#: inc/settings.php:146 inc/settings.php:1066
msgid "Minimal Label"
msgstr ""

#: inc/settings.php:147 inc/settings.php:1067
msgid "Minimal"
msgstr ""

#: inc/settings.php:149
msgid "Change the style of your inline social share buttons. Default: Solid"
msgstr ""

#: inc/settings.php:156
msgid "Button Layout"
msgstr ""

#: inc/settings.php:165
msgid "Auto Width"
msgstr ""

#: inc/settings.php:166
msgid "1 Column"
msgstr ""

#: inc/settings.php:167
msgid "2 Columns"
msgstr ""

#: inc/settings.php:168
msgid "3 Columns"
msgstr ""

#: inc/settings.php:169
msgid "4 Columns"
msgstr ""

#: inc/settings.php:170
msgid "5 Columns"
msgstr ""

#: inc/settings.php:171
msgid "6 Columns"
msgstr ""

#: inc/settings.php:173
msgid "Change the layout of your inline social share buttons."
msgstr ""

#: inc/settings.php:180 inc/settings.php:560 inc/widget.php:88
msgid "Button Alignment"
msgstr ""

#: inc/settings.php:189 inc/settings.php:569 inc/settings.php:1166
#: inc/widget.php:90 js/blocks.js:115 novashare.php:280
msgid "Left"
msgstr ""

#: inc/settings.php:190 inc/settings.php:570 inc/widget.php:91
msgid "Right"
msgstr ""

#: inc/settings.php:191 inc/settings.php:1299 inc/widget.php:92
msgid "Center"
msgstr ""

#: inc/settings.php:194
msgid "Choose how to align your inline social share buttons. Default: Left"
msgstr ""

#: inc/settings.php:201 inc/settings.php:735 inc/settings.php:1076
#: inc/widget.php:103
msgid "Button Size"
msgstr ""

#: inc/settings.php:210 inc/settings.php:744 inc/settings.php:1085
msgid "Small"
msgstr ""

#: inc/settings.php:211 inc/settings.php:745 inc/settings.php:1086
#: inc/settings.php:1563 inc/settings.php:1570
msgid "Medium"
msgstr ""

#: inc/settings.php:212 inc/settings.php:746 inc/settings.php:1087
msgid "Large"
msgstr ""

#: inc/settings.php:214
msgid "Change the size of your inline social share buttons. Default: Medium"
msgstr ""

#: inc/settings.php:221 inc/settings.php:755 inc/settings.php:1096
#: inc/settings.php:1309 inc/widget.php:75
msgid "Button Shape"
msgstr ""

#: inc/settings.php:230 inc/settings.php:764 inc/settings.php:1105
#: inc/settings.php:1318 inc/widget.php:77
msgid "Squared"
msgstr ""

#: inc/settings.php:231 inc/settings.php:765 inc/settings.php:1106
#: inc/settings.php:1319 inc/widget.php:78
msgid "Rounded"
msgstr ""

#: inc/settings.php:232 inc/settings.php:766 inc/settings.php:1107
#: inc/settings.php:1320 inc/widget.php:79
msgid "Circular"
msgstr ""

#: inc/settings.php:234
msgid "Change the shape of your inline social share buttons. Default: Squared"
msgstr ""

#: inc/settings.php:241 inc/settings.php:775 inc/widget.php:121
msgid "Button Color"
msgstr ""

#: inc/settings.php:249
msgid "Change the background color of your inline social share buttons."
msgstr ""

#: inc/settings.php:256 inc/settings.php:790 inc/widget.php:127
msgid "Button Hover Color"
msgstr ""

#: inc/settings.php:264
msgid "Change the hover background color of your inline social share buttons."
msgstr ""

#: inc/settings.php:271 inc/settings.php:805
msgid "Inverse on Hover"
msgstr ""

#: inc/settings.php:278 inc/settings.php:812
msgid ""
"Swap to an inverse button style on hover. This function will add additional "
"inline JavaScript to any page where Novashare buttons are present."
msgstr ""

#: inc/settings.php:285 inc/settings.php:819 inc/widget.php:112
msgid "Button Margin"
msgstr ""

#: inc/settings.php:294
msgid ""
"Change the margin in pixels (px) around your inline social social share "
"buttons."
msgstr ""

#: inc/settings.php:301
msgid "Show Labels"
msgstr ""

#: inc/settings.php:309
msgid ""
"Display network labels on your inline social share buttons. Default: Disabled"
msgstr ""

#: inc/settings.php:316
msgid "Hide Labels on Mobile"
msgstr ""

#: inc/settings.php:324
msgid ""
"Hide network labels on your inline social share buttons on mobile. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:329 inc/settings.php:913 inc/settings.php:1533
msgid "Share Counts"
msgstr ""

#: inc/settings.php:334 inc/settings.php:918
msgid "Total Share Count"
msgstr ""

#: inc/settings.php:342
msgid ""
"Display the total share count with your inline social share buttons. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:349 inc/settings.php:933
msgid "Total Share Count Position"
msgstr ""

#: inc/settings.php:358 inc/settings.php:942
msgid "Before"
msgstr ""

#: inc/settings.php:359 inc/settings.php:943
msgid "After"
msgstr ""

#: inc/settings.php:362
msgid ""
"Change the position of your inline total share count display. Default: After"
msgstr ""

#: inc/settings.php:369 inc/settings.php:953
msgid "Total Share Count Color"
msgstr ""

#: inc/settings.php:378
msgid "Change the text color of your inline total share count display."
msgstr ""

#: inc/settings.php:385 inc/settings.php:969
msgid "Network Share Counts"
msgstr ""

#: inc/settings.php:392 inc/settings.php:976
msgid ""
"Display individual network share counts when hovering over each button. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:397 inc/settings.php:998
msgid "Call to Action"
msgstr ""

#: inc/settings.php:402 inc/settings.php:1003
msgid "Text"
msgstr ""

#: inc/settings.php:411
msgid ""
"Set the call to action text displayed above your inline social share buttons."
msgstr ""

#: inc/settings.php:418
msgid "Font Size"
msgstr ""

#: inc/settings.php:427
msgid "Change the font size of your call to action text."
msgstr ""

#: inc/settings.php:434 inc/settings.php:1035
msgid "Font Color"
msgstr ""

#: inc/settings.php:442
msgid "Change the color of your call to action text."
msgstr ""

#: inc/settings.php:452
msgid "Enable Floating Bar"
msgstr ""

#: inc/settings.php:459
msgid "Display social share buttons on a floating bar. Default: Disabled"
msgstr ""

#: inc/settings.php:473
msgid ""
"Choose which floating social share buttons to display. Click on a square to "
"enable or disable that specific network. Drag and drop squares to arrange "
"the order in which they will display. Default: Twitter, Facebook, LinkedIn"
msgstr ""

#: inc/settings.php:490
msgid ""
"Choose which post types display floating social share buttons. Default: Posts"
msgstr ""

#: inc/settings.php:497
msgid "Show on Posts Page"
msgstr ""

#: inc/settings.php:504
msgid ""
"Show your floating social share buttons on your posts page. Default: Disabled"
msgstr ""

#: inc/settings.php:511
msgid "Show on Archives"
msgstr ""

#: inc/settings.php:518
msgid ""
"Show your floating social share buttons on archive pages. Share counts are "
"not supported on archives. Default: Disabled"
msgstr ""

#: inc/settings.php:526
msgid "Show on Homepage"
msgstr ""

#: inc/settings.php:533
msgid ""
"Show your floating social share buttons on your homepage. Default: Disabled"
msgstr ""

#: inc/settings.php:550 inc/settings.php:1128 inc/settings.php:1389
msgid "Default"
msgstr ""

#: inc/settings.php:553
msgid "Choose where to display your floating social share buttons."
msgstr ""

#: inc/settings.php:572
msgid "Choose how to align your floating social share buttons. Default: Left"
msgstr ""

#: inc/settings.php:590
msgid ""
"Set the width in pixels (px) where you want the floating mobile breakpoint "
"to occur. Default: 1200px"
msgstr ""

#: inc/settings.php:604
msgid ""
"Hide your floating social share buttons when the browser’s viewport is wider "
"than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:619
msgid ""
"Hide your floating social share buttons when the browser’s viewport is "
"narrower than your mobile breakpoint. Default: Disabled"
msgstr ""

#: inc/settings.php:626
msgid "Top Offset"
msgstr ""

#: inc/settings.php:637
msgid ""
"Change the offset in pixels (px) or percentage (%) that your floating social "
"share button container will be from the top of the screen."
msgstr ""

#: inc/settings.php:644
msgid "Edge Offset"
msgstr ""

#: inc/settings.php:653
msgid ""
"Change the offset in pixels (px) that your floating social share button "
"container will be from the edge of the screen."
msgstr ""

#: inc/settings.php:660
msgid "Show on Scroll"
msgstr ""

#: inc/settings.php:668
msgid ""
"Wait to show your floating social share buttons until the user has scrolled "
"past a specific point on the page."
msgstr ""

#: inc/settings.php:675
msgid "Show on Scroll Location"
msgstr ""

#: inc/settings.php:685
msgid "Desktop Only"
msgstr ""

#: inc/settings.php:686
msgid "Mobile Only"
msgstr ""

#: inc/settings.php:689
msgid "Choose what devices are set to only show on scroll."
msgstr ""

#: inc/settings.php:696
msgid "Scroll Threshold"
msgstr ""

#: inc/settings.php:706
msgid "Change the point that show on scroll is triggered. px or %"
msgstr ""

#: inc/settings.php:728
msgid "Change the style of your floating social share buttons. Default: Solid"
msgstr ""

#: inc/settings.php:748
msgid "Change the size of your floating social share buttons. Default: Medium"
msgstr ""

#: inc/settings.php:768
msgid ""
"Change the shape of your floating social share buttons. Default: Squared"
msgstr ""

#: inc/settings.php:783
msgid "Change the background color of your floating social share buttons."
msgstr ""

#: inc/settings.php:798
msgid ""
"Change the hover background color of your floating social share buttons."
msgstr ""

#: inc/settings.php:828
msgid ""
"Change the margin in pixels (px) around your floating social social share "
"buttons."
msgstr ""

#: inc/settings.php:833
msgid "Mobile"
msgstr ""

#: inc/settings.php:838
msgid "Max Width"
msgstr ""

#: inc/settings.php:849
msgid ""
"Set the max width in pixels (px) up to where the floating social share "
"buttons should display on mobile."
msgstr ""

#: inc/settings.php:856 inc/settings.php:1020
msgid "Background Color"
msgstr ""

#: inc/settings.php:864
msgid ""
"Change the background color of the floating social share button container on "
"mobile."
msgstr ""

#: inc/settings.php:871
msgid "Background Padding"
msgstr ""

#: inc/settings.php:880
msgid "Change the padding around your floating social share buttons."
msgstr ""

#: inc/settings.php:887
msgid "Fill Available Space"
msgstr ""

#: inc/settings.php:894
msgid ""
"Allow your floating social share buttons to expand to fill the container."
msgstr ""

#: inc/settings.php:901
msgid "Hide Total Share Count"
msgstr ""

#: inc/settings.php:908
msgid ""
"Hide the total share count from your floating social share buttons on mobile."
msgstr ""

#: inc/settings.php:926
msgid ""
"Display the total share count with your floating social share buttons. "
"Default: Enabled"
msgstr ""

#: inc/settings.php:946
msgid ""
"Change the position of your floating total share count display. Default: "
"After"
msgstr ""

#: inc/settings.php:962
msgid "Change the text color of your floating bar total share count display."
msgstr ""

#: inc/settings.php:993
msgid ""
"Choose which social share buttons to display when the share button is "
"clicked. Click on a square to enable or disable that specific network. Drag "
"and drop squares to arrange the order in which they will display. Default: "
"All Networks"
msgstr ""

#: inc/settings.php:1013
msgid "Change the call to action text displayed in your share button window."
msgstr ""

#: inc/settings.php:1028
msgid ""
"Change the background color of the call to action in your share button "
"window."
msgstr ""

#: inc/settings.php:1043
msgid ""
"Change the font color of the call to action in your share button window."
msgstr ""

#: inc/settings.php:1069
msgid ""
"Change the style of your share button window social share buttons. Default: "
"Solid"
msgstr ""

#: inc/settings.php:1089
msgid ""
"Change the size of your share button window social share buttons. Default: "
"Medium"
msgstr ""

#: inc/settings.php:1109
msgid ""
"Change the shape of your share button window social share buttons. Default: "
"Squared"
msgstr ""

#: inc/settings.php:1119 js/blocks.js:89 novashare.php:268
msgid "Theme"
msgstr ""

#: inc/settings.php:1129
msgid "Simple"
msgstr ""

#: inc/settings.php:1130
msgid "Simple Alternate"
msgstr ""

#: inc/settings.php:1132
msgid "Change the visual style of your click to tweet boxes."
msgstr ""

#: inc/settings.php:1139 js/blocks.js:103 novashare.php:275
msgid "Call to Action Text"
msgstr ""

#: inc/settings.php:1149
msgid ""
"Change the default call to action text displayed on your click to tweet "
"boxes."
msgstr ""

#: inc/settings.php:1156 js/blocks.js:112 novashare.php:277
msgid "Call to Action Position"
msgstr ""

#: inc/settings.php:1165 js/blocks.js:114 novashare.php:279
msgid "Right (Default)"
msgstr ""

#: inc/settings.php:1168
msgid ""
"Change the position of your call to action text displayed on your click to "
"tweet boxes."
msgstr ""

#: inc/settings.php:1175 js/blocks.js:125
msgid "Remove URL"
msgstr ""

#: inc/settings.php:1182
msgid ""
"Change the default status of the remove url option on your click to tweet "
"boxes."
msgstr ""

#: inc/settings.php:1189 js/blocks.js:134 novashare.php:288
msgid "Remove Username"
msgstr ""

#: inc/settings.php:1196
msgid ""
"Change the default status of the remove username option on your click to "
"tweet boxes."
msgstr ""

#: inc/settings.php:1203 js/blocks.js:143 novashare.php:292
msgid "Hide Hashtags"
msgstr ""

#: inc/settings.php:1210
msgid ""
"Change the default status of the hide hashtags option on your click to tweet "
"boxes."
msgstr ""

#: inc/settings.php:1217 js/blocks.js:152 novashare.php:295
msgid "Accent Color"
msgstr ""

#: inc/settings.php:1225
msgid "Change the default accent color used on your click to tweet boxes."
msgstr ""

#: inc/settings.php:1235
msgid "Share Button Behavior"
msgstr ""

#: inc/settings.php:1244
msgid "Share Post Image"
msgstr ""

#: inc/settings.php:1245
msgid "Show All Pinnable Images"
msgstr ""

#: inc/settings.php:1247
msgid ""
"Change what happens when the Pinterest share button is clicked. Default: "
"Share Post Image"
msgstr ""

#: inc/settings.php:1251
msgid "Image Pins"
msgstr ""

#: inc/settings.php:1256
msgid "Enable Image Pins"
msgstr ""

#: inc/settings.php:1264
msgid ""
"Show Pinterest pin buttons when hovering over images in your content. "
"Default: Disabled"
msgstr ""

#: inc/settings.php:1279
msgid "Choose which post types display Pinterest image pins."
msgstr ""

#: inc/settings.php:1295
msgid "Top Left"
msgstr ""

#: inc/settings.php:1296
msgid "Top Right"
msgstr ""

#: inc/settings.php:1297
msgid "Bottom Left"
msgstr ""

#: inc/settings.php:1298
msgid "Bottom Right"
msgstr ""

#: inc/settings.php:1302
msgid ""
"Choose where on the image to display your Pinterest image pin buttons. "
"Default: Top Left"
msgstr ""

#: inc/settings.php:1323
msgid "Change the shape of your Pinterest image pin buttons. Default: Squared"
msgstr ""

#: inc/settings.php:1330
msgid "Hide Button Labels"
msgstr ""

#: inc/settings.php:1339
msgid "Hide the labels on your Pinterest image pin buttons. Default: Disabled"
msgstr ""

#: inc/settings.php:1346
msgid "Excluded Images"
msgstr ""

#: inc/settings.php:1357
msgid ""
"Exclude specific images from getting pins applied. Exclude an image by "
"adding the source URL (example.png) or by adding any unique portion of its "
"attribute string (class=\"example\"). Format: one per line"
msgstr ""

#: inc/settings.php:1367
msgid "Enable Twitter Counts"
msgstr ""

#: inc/settings.php:1374
msgid ""
"Request and store Twitter share counts using a third party service. Default: "
"Disabled"
msgstr ""

#: inc/settings.php:1381
msgid "Twitter Count Service"
msgstr ""

#: inc/settings.php:1393
msgid "Choose which service to use to pull Twitter share counts."
msgstr ""

#: inc/settings.php:1400
msgid "Twitter Username"
msgstr ""

#: inc/settings.php:1409
msgid "novashare"
msgstr ""

#: inc/settings.php:1410
msgid "The username used when sharing content to Twitter."
msgstr ""

#: inc/settings.php:1417
msgid "Facebook App ID"
msgstr ""

#: inc/settings.php:1425
msgid "The Facebook App ID from your Facebook Developer Application."
msgstr ""

#: inc/settings.php:1432
msgid "Facebook App Secret"
msgstr ""

#: inc/settings.php:1440
msgid "The Facebook App Secret from your Facebook Developer Application."
msgstr ""

#: inc/settings.php:1447
msgid "Subscribe Link"
msgstr ""

#: inc/settings.php:1456
msgid "The URL used for your subscribe button."
msgstr ""

#: inc/settings.php:1463
msgid "Global Styles"
msgstr ""

#: inc/settings.php:1469
msgid ""
"Enqueue stylesheet globally to allow for use of shortcodes and widgets in "
"custom templates. Default: Disabled"
msgstr ""

#: inc/settings.php:1476
msgid "Custom CSS"
msgstr ""

#: inc/settings.php:1484
msgid "Add custom CSS to only load when needed."
msgstr ""

#: inc/settings.php:1489
msgid "Meta"
msgstr ""

#: inc/settings.php:1494
msgid "Enable Open Graph"
msgstr ""

#: inc/settings.php:1500
#, php-format
msgid ""
"Print out open graph meta tags with Novashare in the %s section of your "
"site. Default: Enabled"
msgstr ""

#: inc/settings.php:1507
msgid "Hide Meta Box"
msgstr ""

#: inc/settings.php:1513
msgid "Hide Novashare meta box in the WordPress editor. Default: Disabled"
msgstr ""

#: inc/settings.php:1520
msgid "Default Social Image"
msgstr ""

#: inc/settings.php:1527
msgid ""
"Add a default image that will be used for share links and meta tags if no "
"post specific images are found."
msgstr ""

#: inc/settings.php:1538
msgid "Minimum Share Count"
msgstr ""

#: inc/settings.php:1547
msgid ""
"Set a minimum total share count threshold to reach before share counts are "
"displayed."
msgstr ""

#: inc/settings.php:1554
msgid "Share Counts Refresh Rate"
msgstr ""

#: inc/settings.php:1562 inc/settings.php:1569
msgid "High"
msgstr ""

#: inc/settings.php:1564 inc/settings.php:1571
msgid "Low"
msgstr ""

#: inc/settings.php:1566
msgid ""
"Adjust the rate at which your social share counts are refreshed based on the "
"modified date. Default: High"
msgstr ""

#: inc/settings.php:1568
msgid "Modified"
msgstr ""

#: inc/settings.php:1568
msgid "days"
msgstr ""

#: inc/settings.php:1569 inc/settings.php:1570 inc/settings.php:1571
msgid "hours"
msgstr ""

#: inc/settings.php:1579 inc/settings.php:1586
msgid "Purge Share Counts"
msgstr ""

#: inc/settings.php:1587
msgid ""
"Are you sure? This will delete all existing share count data for all posts "
"from the database."
msgstr ""

#: inc/settings.php:1588
msgid "Permanently delete all existing share counts from your database."
msgstr ""

#: inc/settings.php:1593
msgid "Google Analytics"
msgstr ""

#: inc/settings.php:1598
msgid "Enable UTM Tracking"
msgstr ""

#: inc/settings.php:1605
msgid "Add UTM parameters to social sharing links."
msgstr ""

#: inc/settings.php:1612
msgid "Campaign UTM Source"
msgstr ""

#: inc/settings.php:1621
msgid ""
"The value of the UTM source parameter added to your social sharing links. "
"Use {{network}} to dynamically populate the value with the relative social "
"network. Default: {{network}}"
msgstr ""

#: inc/settings.php:1628
msgid "Campaign UTM Medium"
msgstr ""

#: inc/settings.php:1637
msgid ""
"The value of the UTM medium parameter added to your social sharing links. "
"Default: social"
msgstr ""

#: inc/settings.php:1644
msgid "Campaign UTM Name"
msgstr ""

#: inc/settings.php:1653
msgid ""
"The value of the UTM name parameter added to your social sharing links. "
"Default: novashare"
msgstr ""

#: inc/settings.php:1658
msgid "Link Shortening"
msgstr ""

#: inc/settings.php:1663
msgid "Enable Bitly"
msgstr ""

#: inc/settings.php:1670
msgid "Generate Bitly short links for all share URLs."
msgstr ""

#: inc/settings.php:1677
msgid "Generic Access Token"
msgstr ""

#: inc/settings.php:1686
msgid "The Generic Access Token from your Bitly account."
msgstr ""

#: inc/settings.php:1693
msgid "Group"
msgstr ""

#: inc/settings.php:1702
msgid ""
"The group from your Bitly account used to generate and store short links. "
"Non-enterprise users will only have one default group."
msgstr ""

#: inc/settings.php:1709 inc/settings.php:1716
msgid "Purge Short Links"
msgstr ""

#: inc/settings.php:1718
msgid ""
"Are you sure? This will delete all existing short links from the database."
msgstr ""

#: inc/settings.php:1719
msgid "Permanently delete all existing short links from your database."
msgstr ""

#: inc/settings.php:1729
msgid "Combine HTTP & HTTPS"
msgstr ""

#: inc/settings.php:1735
msgid ""
"Combine share counts for HTTP and HTTPS URLs for networks that store them "
"separately. This will double the amount of API calls for those networks."
msgstr ""

#: inc/settings.php:1742
msgid "Recover Previous Permalinks"
msgstr ""

#: inc/settings.php:1749
msgid "Recover share counts for a previous permalink structure."
msgstr ""

#: inc/settings.php:1756
msgid "Previous Permalink Structure"
msgstr ""

#: inc/settings.php:1764
msgid "Select a Structure"
msgstr ""

#: inc/settings.php:1765
msgid "Plain"
msgstr ""

#: inc/settings.php:1766
msgid "Day and name"
msgstr ""

#: inc/settings.php:1767
msgid "Month and name"
msgstr ""

#: inc/settings.php:1768
msgid "Numeric"
msgstr ""

#: inc/settings.php:1769
msgid "Post name"
msgstr ""

#: inc/settings.php:1770
msgid "Custom Structure"
msgstr ""

#: inc/settings.php:1773
msgid "The permalink structure used to recover share counts."
msgstr ""

#: inc/settings.php:1780
msgid "Custom Permalink Structure"
msgstr ""

#: inc/settings.php:1788
msgid ""
"If you are recovering share counts for a custom permalink structure, please "
"provide that structure here."
msgstr ""

#: inc/settings.php:1796
msgid "Recover Previous Domain"
msgstr ""

#: inc/settings.php:1803
msgid "Recover share counts for a previous domain."
msgstr ""

#: inc/settings.php:1810
msgid "Previous Domain"
msgstr ""

#: inc/settings.php:1819
msgid "The domain used to recover share counts."
msgstr ""

#: inc/settings.php:1826 novashare.php:370
msgid "Tools"
msgstr ""

#: inc/settings.php:1840
msgid ""
"Permanently delete all Novashare data from your database when the plugin is "
"uninstalled."
msgstr ""

#: inc/settings.php:1848
msgid "Accessibility Mode"
msgstr ""

#: inc/settings.php:1856
msgid ""
"Disable the use of visual UI elements in the plugin settings such as "
"checkbox toggles and hovering tooltips."
msgstr ""

#: inc/settings.php:1863 inc/settings.php:1870
msgid "Restore Default Options"
msgstr ""

#: inc/settings.php:1871
msgid ""
"Are you sure? This will remove all existing plugin options and restore them "
"to their default states."
msgstr ""

#: inc/settings.php:1873
msgid "Restore all plugin options to their default settings."
msgstr ""

#: inc/settings.php:1880
msgid "Export Settings"
msgstr ""

#: inc/settings.php:1887
msgid "Export Plugin Settings"
msgstr ""

#: inc/settings.php:1889
msgid ""
"Export your Novashare settings for this site as a .json file. This lets you "
"easily import the configuration into another site."
msgstr ""

#: inc/settings.php:1896
msgid "Import Settings"
msgstr ""

#: inc/settings.php:1901
msgid "Import Novashare settings from an exported .json file."
msgstr ""

#: inc/settings.php:2060
msgid "Upload an image."
msgstr ""

#: inc/settings.php:2180
msgid "Import Plugin Settings"
msgstr ""

#: inc/settings.php:2205
#, php-format
msgid "Click %s to view documentation."
msgstr ""

#: inc/support.php:4 inc/support.php:7
msgid "Documentation"
msgstr ""

#: inc/support.php:6
msgid ""
"Need help? Check out our in-depth documentation. Every feature has a step-by-"
"step walkthrough."
msgstr ""

#: inc/support.php:13 inc/support.php:16
msgid "Contact Us"
msgstr ""

#: inc/support.php:15
msgid ""
"If you have questions or problems, please send us a message. We’ll get back "
"to you as soon as possible."
msgstr ""

#: inc/support.php:22
msgid "Frequently Asked Questions"
msgstr ""

#: inc/support.php:26
msgid "How do I license activate the plugin?"
msgstr ""

#: inc/support.php:27
msgid "How do I update the plugin?"
msgstr ""

#: inc/support.php:28
msgid "How do I upgrade my license?"
msgstr ""

#: inc/support.php:29
msgid "Where can I view the changelog?"
msgstr ""

#: inc/support.php:30
msgid "Where can I sign up for the affiliate program?"
msgstr ""

#: inc/support.php:33
msgid "How do I enable inline share buttons?"
msgstr ""

#: inc/support.php:34
msgid "How do I enable floating bar share buttons?"
msgstr ""

#: inc/support.php:35
msgid "How do I enable share counts (total + network)?"
msgstr ""

#: inc/support.php:36
msgid "How do I customize Click to Tweet settings?"
msgstr ""

#: inc/support.php:37
msgid "How do I enable Pinterest image hover pins?"
msgstr ""

#: inc/widget.php:8
msgid "Novashare Follow Widget"
msgstr ""

#: inc/widget.php:9
msgid "Add follow buttons for your social network profiles."
msgstr ""

#: inc/widget.php:67
msgid "Title"
msgstr ""

#: inc/widget.php:133
msgid "Icon Color"
msgstr ""

#: inc/widget.php:139
msgid "Icon Hover Color"
msgstr ""

#: inc/widget.php:146
msgid "Open Links in New Tab"
msgstr ""

#: inc/widget.php:175
msgid "Select a Network"
msgstr ""

#: inc/widget.php:194
msgid "Add Network"
msgstr ""

#: inc/widget.php:273
msgid "That network is already in use."
msgstr ""

#: inc/widget.php:288
msgid "Page Name"
msgstr ""

#: inc/widget.php:291
msgid "Username"
msgstr ""

#: js/admin.js:297
msgid "Characters"
msgstr ""

#: js/admin.js:300
msgid "Character"
msgstr ""

#: js/admin.js:304
msgid "Remaining"
msgstr ""

#: js/blocks-pinterest.js:185
msgid "Pin Image"
msgstr ""

#: js/blocks-pinterest.js:195
msgid "Upload Image"
msgstr ""

#: js/blocks-pinterest.js:211
msgid "Replace Image"
msgstr ""

#: js/blocks-pinterest.js:217
msgid "Remove image"
msgstr ""

#: js/blocks.js:32
msgid "Add a tweet box."
msgstr ""

#: js/blocks.js:85 novashare.php:326
msgid "Settings"
msgstr ""

#: js/blocks.js:91
msgid "Default (Accent Background)"
msgstr ""

#: js/blocks.js:92 novashare.php:271
msgid "Simple (Transparent Background)"
msgstr ""

#: js/blocks.js:93 novashare.php:272
msgid "Simple Alternate (Gray Background)"
msgstr ""

#: js/blocks.js:275 novashare.php:296
msgid "Characters Remaining"
msgstr ""

#: novashare.php:263
msgid "Click to Tweet Shortcode"
msgstr ""

#: novashare.php:264
msgid "Insert Shortcode"
msgstr ""

#: novashare.php:266
msgid "Tweet"
msgstr ""

#: novashare.php:270
msgid "Default (Blue Background)"
msgstr ""

#: novashare.php:284
msgid "Remove Post URL"
msgstr ""

#: novashare.php:285
msgid "The URL of the current post will not be added to the tweet."
msgstr ""

#: novashare.php:289
msgid "The Twitter username saved in Novashare will not be added to the tweet."
msgstr ""

#: novashare.php:293
msgid "Trailing hashtags will be hidden from the display box."
msgstr ""

#: novashare.php:338 novashare.php:377
msgid "Support"
msgstr ""

#: novashare.php:366
msgid "Network"
msgstr ""

#: novashare.php:369
msgid "Options"
msgstr ""

#: novashare.php:381
msgid "Speed Up Guide"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "Novashare"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://novashare.io/"
msgstr ""

#. Description of the plugin/theme
msgid "Novashare is a lightweight and fast social media sharing plugin."
msgstr ""

#. Author of the plugin/theme
msgid "forgemedia"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://forgemedia.io/"
msgstr ""
