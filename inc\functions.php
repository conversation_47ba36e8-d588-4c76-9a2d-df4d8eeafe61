<?php
$novashare_options = get_option('novashare_options');

//network details
function novashare_networks($type = 'share') {
	$networks = array(
		'500px'      => array(
			'name'   => '500px',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M103.3 344.3c-6.5-14.2-6.9-18.3 7.4-23.1 25.6-8 8 9.2 43.2 49.2h.3v-93.9c1.2-50.2 44-92.2 97.7-92.2 53.9 0 97.7 43.5 97.7 96.8 0 63.4-60.8 113.2-128.5 93.3-10.5-4.2-2.1-31.7 8.5-28.6 53 0 89.4-10.1 89.4-64.4 0-61-77.1-89.6-116.9-44.6-23.5 26.4-17.6 42.1-17.6 157.6 50.7 31 118.3 22 160.4-20.1 24.8-24.8 38.5-58 38.5-93 0-35.2-13.8-68.2-38.8-93.3-24.8-24.8-57.8-38.5-93.3-38.5s-68.8 13.8-93.5 38.5c-.3.3-16 16.5-21.2 23.9l-.5.6c-3.3 4.7-6.3 9.1-20.1 6.1-6.9-1.7-14.3-5.8-14.3-11.8V20c0-5 3.9-10.5 10.5-10.5h241.3c8.3 0 8.3 11.6 8.3 15.1 0 3.9 0 15.1-8.3 15.1H130.3v132.9h.3c104.2-109.8 282.8-36 282.8 108.9 0 178.1-244.8 220.3-310.1 62.8m63.3-260.8c-.5 4.2 4.6 24.5 14.6 20.6C306 56.6 384 144.5 390.6 144.5c4.8 0 22.8-15.3 14.3-22.8-93.2-89-234.5-57-238.3-38.2M393 414.7C283 524.6 94 475.5 61 310.5c0-12.2-30.4-7.4-28.9 3.3 24 173.4 246 256.9 381.6 121.3 6.9-7.8-12.6-28.4-20.7-20.4M213.6 306.6c0 4 4.3 7.3 5.5 8.5 3 3 6.1 4.4 8.5 4.4 3.8 0 2.6.2 22.3-19.5 19.6 19.3 19.1 19.5 22.3 19.5 5.4 0 18.5-10.4 10.7-18.2L265.6 284l18.2-18.2c6.3-6.8-10.1-21.8-16.2-15.7L249.7 268c-18.6-18.8-18.4-19.5-21.5-19.5-5 0-18 11.7-12.4 17.3L234 284c-18.1 17.9-20.4 19.2-20.4 22.6"/></svg>',
			'follow' => true
		),
		'angellist'  => array(
			'name'   => 'AngelList',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M347.1 215.4c11.7-32.6 45.4-126.9 45.4-157.1 0-26.6-15.7-48.9-43.7-48.9-44.6 0-84.6 131.7-97.1 163.1C242 144 196.6 0 156.6 0c-31.1 0-45.7 22.9-45.7 51.7 0 35.3 34.2 126.8 46.6 162-6.3-2.3-13.1-4.3-20-4.3-23.4 0-48.3 29.1-48.3 52.6 0 8.9 4.9 21.4 8 29.7-36.9 10-51.1 34.6-51.1 71.7C46 435.6 114.4 512 210.6 512c118 0 191.4-88.6 191.4-202.9 0-43.1-6.9-82-54.9-93.7M311.7 108c4-12.3 21.1-64.3 37.1-64.3 8.6 0 10.9 8.9 10.9 16 0 19.1-38.6 124.6-47.1 148l-34-6zM142.3 48.3c0-11.9 14.5-45.7 46.3 47.1l34.6 100.3c-15.6-1.3-27.7-3-35.4 1.4-10.9-28.8-45.5-119.7-45.5-148.8M140 244c29.3 0 67.1 94.6 67.1 107.4 0 5.1-4.9 11.4-10.6 11.4-20.9 0-76.9-76.9-76.9-97.7.1-7.7 12.7-21.1 20.4-21.1m184.3 186.3c-29.1 32-66.3 48.6-109.7 48.6-59.4 0-106.3-32.6-128.9-88.3-17.1-43.4 3.8-68.3 20.6-68.3 11.4 0 54.3 60.3 54.3 73.1 0 4.9-7.7 8.3-11.7 8.3-16.1 0-22.4-15.5-51.1-51.4-29.7 29.7 20.5 86.9 58.3 86.9 26.1 0 43.1-24.2 38-42 3.7 0 8.3.3 11.7-.6 1.1 27.1 9.1 59.4 41.7 61.7 0-.9 2-7.1 2-7.4 0-17.4-10.6-32.6-10.6-50.3 0-28.3 21.7-55.7 43.7-71.7 8-6 17.7-9.7 27.1-13.1 9.7-3.7 20-8 27.4-15.4-1.1-11.2-5.7-21.1-16.9-21.1-27.7 0-120.6 4-120.6-39.7 0-6.7.1-13.1 17.4-13.1 32.3 0 114.3 8 138.3 29.1 18.1 16.1 24.3 113.2-31 174.7m-98.6-126c9.7 3.1 19.7 4 29.7 6-7.4 5.4-14 12-20.3 19.1-2.8-8.5-6.2-16.8-9.4-25.1"/></svg>',
			'follow' => true
		),
		'applemusic' => array(
			'name'   => 'Apple Music',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M381.9 388.2c-6.4 27.4-27.2 42.8-55.1 48-24.5 4.5-44.9 5.6-64.5-10.2-23.9-20.1-24.2-53.4-2.7-74.4 17-16.2 40.9-19.5 76.8-25.8 6-1.1 11.2-2.5 15.6-7.4 6.4-7.2 4.4-4.1 4.4-163.2 0-11.2-5.5-14.3-17-12.3-8.2 1.4-185.7 34.6-185.7 34.6-10.2 2.2-13.4 5.2-13.4 16.7 0 234.7 1.1 223.9-2.5 239.5-4.2 18.2-15.4 31.9-30.2 39.5-16.8 9.3-47.2 13.4-63.4 10.4-43.2-8.1-58.4-58-29.1-86.6 17-16.2 40.9-19.5 76.8-25.8 6-1.1 11.2-2.5 15.6-7.4 10.1-11.5 1.8-256.6 5.2-270.2.8-5.2 3-9.6 7.1-12.9 4.2-3.5 11.8-5.5 13.4-5.5 204-38.2 228.9-43.1 232.4-43.1 11.5-.8 18.1 6 18.1 17.6.2 344.5 1.1 326-1.8 338.5"/></svg>',
			'follow' => true
		),
		'bandcamp'   => array(
			'name'   => 'Bandcamp',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8m48.2 326.1h-181L207.9 178h181Z"/></svg>',
			'follow' => true
		),
		'behance'    => array(
			'name'   => 'Behance',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M232 237.2c31.8-15.2 48.4-38.2 48.4-74 0-70.6-52.6-87.8-113.3-87.8H0v354.4h171.8c64.4 0 124.9-30.9 124.9-102.9 0-44.5-21.1-77.4-64.7-89.7M77.9 135.9H151c28.1 0 53.4 7.9 53.4 40.5 0 30.1-19.7 42.2-47.5 42.2h-79zm83.3 233.7H77.9V272h84.9c34.3 0 56 14.3 56 50.6 0 35.8-25.9 47-57.6 47m358.5-240.7H376V94h143.7zM576 305.2c0-75.9-44.4-139.2-124.9-139.2-78.2 0-131.3 58.8-131.3 135.8 0 79.9 50.3 134.7 131.3 134.7 61.3 0 101-27.6 120.1-86.3H509c-6.7 21.9-34.3 33.5-55.7 33.5-41.3 0-63-24.2-63-65.3h185.1c.3-4.2.6-8.7.6-13.2M390.4 274c2.3-33.7 24.7-54.8 58.5-54.8 35.4 0 53.2 20.8 56.2 54.8z"/></svg>',
			'follow' => true
		),
		'bitbucket'  => array(
			'name'   => 'Bitbucket',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M22.2 32A16 16 0 0 0 6 47.8a26 26 0 0 0 .2 2.8l67.9 412.1a21.77 21.77 0 0 0 21.3 18.2h325.7a16 16 0 0 0 16-13.4L505 50.7a16 16 0 0 0-13.2-18.3 25 25 0 0 0-2.8-.2zm285.9 297.8h-104l-28.1-147h157.3z"/></svg>',
			'follow' => true
		),
		'bluesky'  => array(
			'name'   => 'Bluesky',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M407.8 294.7c-3.3-.4-6.7-.8-10-1.3 3.4.4 6.7.9 10 1.3M288 227.1c-26.1-50.7-97.1-145.2-163.1-191.8C61.6-9.4 37.5-1.7 21.6 5.5 3.3 13.8 0 41.9 0 58.4S9.1 194 15 213.9c19.5 65.7 89.1 87.9 153.2 80.7 3.3-.5 6.6-.9 10-1.4-3.3.5-6.6 1-10 1.4-93.9 14-177.3 48.2-67.9 169.9C220.6 589.1 265.1 437.8 288 361.1c22.9 76.7 49.2 222.5 185.6 103.4 102.4-103.4 28.1-156-65.8-169.9-3.3-.4-6.7-.8-10-1.3 3.4.4 6.7.9 10 1.3 64.1 7.1 133.6-15.1 153.2-80.7C566.9 194 576 75 576 58.4s-3.3-44.7-21.6-52.9c-15.8-7.1-40-14.9-103.2 29.8C385.1 81.9 314.1 176.4 288 227.1"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'buffer'     => array(
			'name'   => 'Buffer',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="m427.84 380.67-196.5 97.82a18.6 18.6 0 0 1-14.67 0L20.16 380.67c-4-2-4-5.28 0-7.29L67.22 350a18.65 18.65 0 0 1 14.69 0l134.76 67a18.5 18.5 0 0 0 14.67 0l134.76-67a18.62 18.62 0 0 1 14.68 0l47.06 23.43c4.05 1.96 4.05 5.24 0 7.24m0-136.53-47.06-23.43a18.62 18.62 0 0 0-14.68 0l-134.76 67.08a18.68 18.68 0 0 1-14.67 0L81.91 220.71a18.65 18.65 0 0 0-14.69 0l-47.06 23.43c-4 2-4 5.29 0 7.31l196.51 97.8a18.6 18.6 0 0 0 14.67 0l196.5-97.8c4.05-2.02 4.05-5.3 0-7.31M20.16 130.42l196.5 90.29a20.08 20.08 0 0 0 14.67 0l196.51-90.29c4-1.86 4-4.89 0-6.74L231.33 33.4a19.88 19.88 0 0 0-14.67 0l-196.5 90.28c-4.05 1.85-4.05 4.88 0 6.74"/></svg>',
			'share'  => true,
			'highlight' => true
		),
		'codepen'    => array(
			'name'   => 'CodePen',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="m502.285 159.704-234-156c-7.987-4.915-16.511-4.96-24.571 0l-234 156C3.714 163.703 0 170.847 0 177.989v155.999c0 7.143 3.714 14.286 9.715 18.286l234 156.022c7.987 4.915 16.511 4.96 24.571 0l234-156.022c6-3.999 9.715-11.143 9.715-18.286V177.989c-.001-7.142-3.715-14.286-9.716-18.285M278 63.131l172.286 114.858-76.857 51.429L278 165.703zm-44 0v102.572l-95.429 63.715-76.857-51.429zM44 219.132l55.143 36.857L44 292.846zm190 229.715L61.714 333.989l76.857-51.429L234 346.275zm22-140.858-77.715-52 77.715-52 77.715 52zm22 140.858V346.275l95.429-63.715 76.857 51.429zm190-156.001-55.143-36.857L468 219.132z"/></svg>',
			'follow' => true
		),
		'copy'       => array(
			'name'   => 'Copy',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24m120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97"/></svg>',
			'share'  => true
		),
		'deviantart' => array(
			'name'   => 'DeviantArt',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="m320 93.2-98.2 179.1 7.4 9.5H320v127.7H159.1l-13.5 9.2-43.7 84c-.3 0-8.6 8.6-9.2 9.2H0v-93.2l93.2-179.4-7.4-9.2H0V102.5h156l13.5-9.2 43.7-84c.3 0 8.6-8.6 9.2-9.2H320z"/></svg>',
			'follow' => true
		),
		'digg'       => array(
			'name'   => 'Digg',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M81.7 172.3H0v174.4h132.7V96h-51zm0 133.4H50.9v-92.3h30.8zm297.2-133.4v174.4h81.8v28.5h-81.8V416H512V172.3zm81.8 133.4h-30.8v-92.3h30.8zm-235.6 41h82.1v28.5h-82.1V416h133.3V172.3H225.1zm51.2-133.3h30.8v92.3h-30.8zM153.3 96h51.3v51h-51.3zm0 76.3h51.3v174.4h-51.3z"/></svg>',
			'follow' => true
		),
		'discord'    => array(
			'name'   => 'Discord',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M524.531 69.836a1.5 1.5 0 0 0-.764-.7A485 485 0 0 0 404.081 32.03a1.82 1.82 0 0 0-1.923.91 338 338 0 0 0-14.9 30.6 447.9 447.9 0 0 0-134.426 0 310 310 0 0 0-15.135-30.6 1.89 1.89 0 0 0-1.924-.91 483.7 483.7 0 0 0-119.688 37.107 1.7 1.7 0 0 0-.788.676C39.068 183.651 18.186 294.69 28.43 404.354a2.02 2.02 0 0 0 .765 1.375 487.7 487.7 0 0 0 146.825 74.189 1.9 1.9 0 0 0 2.063-.676A348 348 0 0 0 208.12 430.4a1.86 1.86 0 0 0-1.019-2.588 321 321 0 0 1-45.868-21.853 1.885 1.885 0 0 1-.185-3.126 251 251 0 0 0 9.109-7.137 1.82 1.82 0 0 1 1.9-.256c96.229 43.917 200.41 43.917 295.5 0a1.81 1.81 0 0 1 1.924.233 235 235 0 0 0 9.132 7.16 1.884 1.884 0 0 1-.162 3.126 301.4 301.4 0 0 1-45.89 21.83 1.875 1.875 0 0 0-1 2.611 391 391 0 0 0 30.014 48.815 1.86 1.86 0 0 0 2.063.7A486 486 0 0 0 610.7 405.729a1.88 1.88 0 0 0 .765-1.352c12.264-126.783-20.532-236.912-86.934-334.541M222.491 337.58c-28.972 0-52.844-26.587-52.844-59.239s23.409-59.241 52.844-59.241c29.665 0 53.306 26.82 52.843 59.239 0 32.654-23.41 59.241-52.843 59.241m195.38 0c-28.971 0-52.843-26.587-52.843-59.239s23.409-59.241 52.843-59.241c29.667 0 53.307 26.82 52.844 59.239 0 32.654-23.177 59.241-52.844 59.241"/></svg>',
			'follow' => true
		),
		'dribbble'   => array(
			'name'   => 'Dribbble',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 8C119.252 8 8 119.252 8 256s111.252 248 248 248 248-111.252 248-248S392.748 8 256 8m163.97 114.366c29.503 36.046 47.369 81.957 47.835 131.955-6.984-1.477-77.018-15.682-147.502-6.818-5.752-14.041-11.181-26.393-18.617-41.614 78.321-31.977 113.818-77.482 118.284-83.523M396.421 97.87c-3.81 5.427-35.697 48.286-111.021 76.519-34.712-63.776-73.185-116.168-79.04-124.008 67.176-16.193 137.966 1.27 190.061 47.489m-230.48-33.25c5.585 7.659 43.438 60.116 78.537 122.509-99.087 26.313-186.36 25.934-195.834 25.809C62.38 147.205 106.678 92.573 165.941 64.62M44.17 256.323c0-2.166.043-4.322.108-6.473 9.268.19 111.92 1.513 217.706-30.146 6.064 11.868 11.857 23.915 17.174 35.949-76.599 21.575-146.194 83.527-180.531 142.306C64.794 360.405 44.17 310.73 44.17 256.323m81.807 167.113c22.127-45.233 82.178-103.622 167.579-132.756 29.74 77.283 42.039 142.053 45.189 160.638-68.112 29.013-150.015 21.053-212.768-27.882m248.38 8.489c-2.171-12.886-13.446-74.897-41.152-151.033 66.38-10.626 124.7 6.768 131.947 9.055-9.442 58.941-43.273 109.844-90.795 141.978"/></svg>',
			'follow' => true
		),
		'dropbox'    => array(
			'name'   => 'Dropbox',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 528 512"><path fill="currentColor" d="m264.4 116.3-132 84.3 132 84.3-132 84.3L0 284.1l132.3-84.3L0 116.3 132.3 32zM131.6 395.7l132-84.3 132 84.3-132 84.3zm132.8-111.6 132-84.3-132-83.6L395.7 32 528 116.3l-132.3 84.3L528 284.8l-132.3 84.3z"/></svg>',
			'follow' => true
		),
		'email'	     => array(
			'name'   => 'Email',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'facebook'   => array(
			'name'   => 'Facebook',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="m279.14 288 14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'flickr'     => array(
			'name'   => 'Flickr',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48M144.5 319c-35.1 0-63.5-28.4-63.5-63.5s28.4-63.5 63.5-63.5 63.5 28.4 63.5 63.5-28.4 63.5-63.5 63.5m159 0c-35.1 0-63.5-28.4-63.5-63.5s28.4-63.5 63.5-63.5 63.5 28.4 63.5 63.5-28.4 63.5-63.5 63.5"/></svg>',
			'follow' => true
		),
		'flipboard'  => array(
			'name'   => 'Flipboard',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 32v448h448V32zm358.4 179.2h-89.6v89.6h-89.6v89.6H89.6V121.6h268.8z"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'foursquare' => array(
			'name'   => 'Foursquare',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 368 512"><path fill="currentColor" d="M323.1 3H49.9C12.4 3 0 31.3 0 49.1v433.8c0 20.3 12.1 27.7 18.2 30.1 6.2 2.5 22.8 4.6 32.9-7.1C180 356.5 182.2 354 182.2 354c3.1-3.4 3.4-3.1 6.8-3.1h83.4c35.1 0 40.6-25.2 44.3-39.7l48.6-243C373.8 25.8 363.1 3 323.1 3m-16.3 73.8-11.4 59.7c-1.2 6.5-9.5 13.2-16.9 13.2H172.1c-12 0-20.6 8.3-20.6 20.3v13c0 12 8.6 20.6 20.6 20.6h90.4c8.3 0 16.6 9.2 14.8 18.2-1.8 8.9-10.5 53.8-11.4 58.8-.9 4.9-6.8 13.5-16.9 13.5h-73.5c-13.5 0-17.2 1.8-26.5 12.6 0 0-8.9 11.4-89.5 108.3-.9.9-1.8.6-1.8-.3V75.9c0-7.7 6.8-16.6 16.6-16.6h219c8.2 0 15.6 7.7 13.5 17.5"/></svg>',
			'follow' => true
		),
		'github'     => array(
			'name'   => 'GitHub',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><path fill="currentColor" d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>',
			'follow' => true
		),
		'goodreads'  => array(
			'name'   => 'Goodreads',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M42.6 403.3h2.8c12.7 0 25.5 0 38.2.1 1.6 0 3.1-.4 3.6 2.1 7.1 34.9 30 54.6 62.9 63.9 26.9 7.6 54.1 7.8 81.3 1.8 33.8-7.4 56-28.3 68-60.4 8-21.5 10.7-43.8 11-66.5.1-5.8.3-47-.2-52.8l-.9-.3c-.8 1.5-1.7 2.9-2.5 4.4-22.1 43.1-61.3 67.4-105.4 69.1-103 4-169.4-57-172-176.2-.5-23.7 1.8-46.9 8.3-69.7C58.3 47.7 112.3.6 191.6 0c61.3-.4 101.5 38.7 116.2 70.3.5 1.1 1.3 2.3 2.4 1.9V10.6h44.3c0 280.3.1 332.2.1 332.2-.1 78.5-26.7 143.7-103 162.2-69.5 16.9-159 4.8-196-57.2-8-13.5-11.8-28.3-13-44.5M188.9 36.5c-52.5-.5-108.5 40.7-115 133.8-4.1 59 14.8 122.2 71.5 148.6 27.6 12.9 74.3 15 108.3-8.7 47.6-33.2 62.7-97 54.8-154-9.7-71.1-47.8-120-119.6-119.7"/></svg>',
			'follow' => true
		),
		'hackernews' => array(
			'name'   => 'Hacker News',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48M21.2 229.2H21c.1-.1.2-.3.3-.4 0 .1 0 .3-.1.4m218 53.9V384h-31.4V281.3L128 128h37.3c52.5 98.3 49.2 101.2 59.3 125.6 12.3-27 5.8-24.4 60.6-125.6H320z"/></svg>',
			'share'  => true,
			'highlight' => true
		),
		'houzz'      => array(
			'name'   => 'Houzz',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M275.9 330.7H171.3V480H17V32h109.5v104.5l305.1 85.6V480H275.9z"/></svg>',
			'follow' => true
		),
		'instagram'  => array(
			'name'   => 'Instagram',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141m0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7m146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8m76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8M398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1"/></svg>',
			'follow' => true
		),
		'jsfiddle'   => array(
			'name'   => 'JSFiddle',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M510.634 237.462c-4.727-2.621-5.664-5.748-6.381-10.776-2.352-16.488-3.539-33.619-9.097-49.095-35.895-99.957-153.99-143.386-246.849-91.646-27.37 15.25-48.971 36.369-65.493 63.903-3.184-1.508-5.458-2.71-7.824-3.686-30.102-12.421-59.049-10.121-85.331 9.167-25.531 18.737-36.422 44.548-32.676 76.408.355 3.025-1.967 7.621-4.514 9.545-39.712 29.992-56.031 78.065-41.902 124.615 13.831 45.569 57.514 79.796 105.608 81.433 30.291 1.031 60.637.546 90.959.539 84.041-.021 168.09.531 252.12-.48 52.664-.634 96.108-36.873 108.212-87.293 11.54-48.074-11.144-97.3-56.832-122.634m21.107 156.88c-18.23 22.432-42.343 35.253-71.28 35.65-56.874.781-113.767.23-170.652.23 0 .7-163.028.159-163.728.154-43.861-.332-76.739-19.766-95.175-59.995-18.902-41.245-4.004-90.848 34.186-116.106 9.182-6.073 12.505-11.566 10.096-23.136-5.49-26.361 4.453-47.956 26.42-62.981 22.987-15.723 47.422-16.146 72.034-3.083 10.269 5.45 14.607 11.564 22.198-2.527 14.222-26.399 34.557-46.727 60.671-61.294 97.46-54.366 228.37 7.568 230.24 132.697.122 8.15 2.412 12.428 9.848 15.894 57.56 26.829 74.456 96.122 35.142 144.497m-87.789-80.499c-5.848 31.157-34.622 55.096-66.666 55.095-16.953-.001-32.058-6.545-44.079-17.705-27.697-25.713-71.141-74.98-95.937-93.387-20.056-14.888-41.99-12.333-60.272 3.782-49.996 44.071 15.859 121.775 67.063 77.188 4.548-3.96 7.84-9.543 12.744-12.844 8.184-5.509 20.766-.884 13.168 10.622-17.358 26.284-49.33 38.197-78.863 29.301-28.897-8.704-48.84-35.968-48.626-70.179 1.225-22.485 12.364-43.06 35.414-55.965 22.575-12.638 46.369-13.146 66.991 2.474C295.68 280.7 320.467 323.97 352.185 343.47c24.558 15.099 54.254 7.363 68.823-17.506 28.83-49.209-34.592-105.016-78.868-63.46-3.989 3.744-6.917 8.932-11.41 11.72-10.975 6.811-17.333-4.113-12.809-10.353 20.703-28.554 50.464-40.44 83.271-28.214 31.429 11.714 49.108 44.366 42.76 78.186"/></svg>',
			'follow' => true
		),
		'lastfm'     => array(
			'name'   => 'Last.fm',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="m225.8 367.1-18.8-51s-30.5 34-76.2 34c-40.5 0-69.2-35.2-69.2-91.5 0-72.1 36.4-97.9 72.1-97.9 66.5 0 74.8 53.3 100.9 134.9 18.8 56.9 54 102.6 155.4 102.6 72.7 0 122-22.3 122-80.9 0-72.9-62.7-80.6-115-92.1-25.8-5.9-33.4-16.4-33.4-34 0-19.9 15.8-31.7 41.6-31.7 28.2 0 43.4 10.6 45.7 35.8l58.6-7c-4.7-52.8-41.1-74.5-100.9-74.5-52.8 0-104.4 19.9-104.4 83.9 0 39.9 19.4 65.1 68 76.8 44.9 10.6 79.8 13.8 79.8 45.7 0 21.7-21.1 30.5-61 30.5-59.2 0-83.9-31.1-97.9-73.9-32-96.8-43.6-163-161.3-163C45.7 113.8 0 168.3 0 261c0 89.1 45.7 137.2 127.9 137.2 66.2 0 97.9-31.1 97.9-31.1"/></svg>',
			'follow' => true
		),
		'line'   => array(
			'name'   => 'Line',
			'icon'   => '<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M19.365 9.863a.631.631 0 0 1 0 1.261H17.61v1.125h1.755a.63.63 0 1 1 0 1.259h-2.386a.63.63 0 0 1-.627-.629V8.108c0-.345.282-.63.63-.63h2.386a.63.63 0 0 1-.003 1.26H17.61v1.125zm-3.855 3.016a.63.63 0 0 1-.631.627.62.62 0 0 1-.51-.25l-2.443-3.317v2.94a.63.63 0 0 1-1.257 0V8.108a.627.627 0 0 1 .624-.628c.195 0 .375.104.495.254l2.462 3.33V8.108c0-.345.282-.63.63-.63.345 0 .63.285.63.63zm-5.741 0a.63.63 0 0 1-.631.629.63.63 0 0 1-.627-.629V8.108c0-.345.282-.63.63-.63.346 0 .628.285.628.63zm-2.466.629H4.917a.634.634 0 0 1-.63-.629V8.108c0-.345.285-.63.63-.63.348 0 .63.285.63.63v4.141h1.756a.63.63 0 0 1 0 1.259M24 10.314C24 4.943 18.615.572 12 .572S0 4.943 0 10.314c0 4.811 4.27 8.842 10.035 9.608.391.082.923.258 1.058.59.12.301.079.766.038 1.08l-.164 1.02c-.045.301-.24 1.186 1.049.645 1.291-.539 6.916-4.078 9.436-6.975C23.176 14.393 24 12.458 24 10.314"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'linkedin'   => array(
			'name'   => 'LinkedIn',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3M447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'location'   => array(
			'name'   => 'Location',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"/></svg>',
			'follow' => true
		),
		'mastodon'   => array(
			'name'   => 'Mastodon',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.5 102.5 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5m-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'messenger'		 => array(
			'name'   => 'Messenger',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256.55 8C116.52 8 8 110.34 8 248.57c0 72.3 29.71 134.78 78.07 177.94 8.35 7.51 6.63 11.86 8.05 58.23A19.92 19.92 0 0 0 122 502.31c52.91-23.3 53.59-25.14 62.56-22.7C337.85 521.8 504 423.7 504 248.57 504 110.34 396.59 8 256.55 8m149.24 185.13-73 115.57a37.37 37.37 0 0 1-53.91 9.93l-58.08-43.47a15 15 0 0 0-18 0l-78.37 59.44c-10.46 7.93-24.16-4.6-17.11-15.67l73-115.57a37.36 37.36 0 0 1 53.91-9.93l58.06 43.46a15 15 0 0 0 18 0l78.41-59.38c10.44-7.98 24.14 4.54 17.09 15.62"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'mix'		 => array(
			'name'   => 'Mix',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 64v348.9c0 56.2 88 58.1 88 0V174.3c7.9-52.9 88-50.4 88 6.5v175.3c0 57.9 96 58 96 0V240c5.3-54.7 88-52.5 88 4.3v23.8c0 59.9 88 56.6 88 0V64z"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'mixcloud'   => array(
			'name'   => 'Mixcloud',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M424.43 219.729C416.124 134.727 344.135 68 256.919 68c-72.266 0-136.224 46.516-159.205 114.074-54.545 8.029-96.63 54.822-96.63 111.582 0 62.298 50.668 112.966 113.243 112.966h289.614c52.329 0 94.969-42.362 94.969-94.693 0-45.131-32.118-83.063-74.48-92.2m-20.489 144.53H114.327c-39.04 0-70.881-31.564-70.881-70.604s31.841-70.604 70.881-70.604c18.827 0 36.548 7.475 49.838 20.766 19.963 19.963 50.133-10.227 30.18-30.18-14.675-14.398-32.672-24.365-52.053-29.349 19.935-44.3 64.79-73.926 114.628-73.926 69.496 0 125.979 56.483 125.979 125.702 0 13.568-2.215 26.857-6.369 39.594-8.943 27.517 32.133 38.939 40.147 13.29 2.769-8.306 4.984-16.889 6.369-25.472 19.381 7.476 33.502 26.303 33.502 48.453 0 28.795-23.535 52.33-52.607 52.33m235.069-52.33c0 44.024-12.737 86.386-37.102 122.657-4.153 6.092-10.798 9.414-17.72 9.414-16.317 0-27.127-18.826-17.443-32.949 19.381-29.349 29.903-63.682 29.903-99.122s-10.521-69.773-29.903-98.845c-15.655-22.831 19.361-47.24 35.163-23.534 24.366 35.993 37.102 78.356 37.102 122.379m-70.88 0c0 31.565-9.137 62.021-26.857 88.325-4.153 6.091-10.798 9.136-17.72 9.136-17.201 0-27.022-18.979-17.443-32.948 13.013-19.104 19.658-41.255 19.658-64.513 0-22.981-6.645-45.408-19.658-64.512-15.761-22.986 19.008-47.095 35.163-23.535 17.719 26.026 26.857 56.483 26.857 88.047"/></svg>',
			'follow' => true
		),
		'nextdoor' => array(
			'name'   => 'Nextdoor',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 651 512"><path fill="currentColor" d="M399.91.117c141.074 0 249.98 101.121 249.98 229.73V499.5a12.1 12.1 0 0 1-.94 4.691c-.622 1.48-1.505 2.844-2.641 3.98a12.5 12.5 0 0 1-3.95 2.673 12.6 12.6 0 0 1-4.656.992H525.477c-1.618 0-3.211-.309-4.688-.938a12.6 12.6 0 0 1-3.973-2.671c-1.136-1.137-2.015-2.5-2.64-4.008-.598-1.512-.91-3.102-.88-4.719V248.64c0-55.745-42.53-119.003-113.394-119.003-74.27 0-113.394 63.226-113.394 119.004v250.797c0 1.62-.34 3.214-.965 4.695a12.5 12.5 0 0 1-2.668 3.976 12.5 12.5 0 0 1-3.973 2.672 12 12 0 0 1-4.687.965H162.043c-1.191 0-2.41-.168-3.55-.512a11.3 11.3 0 0 1-3.235-1.535c-.996-.683-1.903-1.476-2.668-2.414-.77-.937-1.39-1.96-1.817-3.098-.14-.367-.289-.769-.398-1.136-.117-.399-.227-.77-.285-1.168a9 9 0 0 1-.172-1.192 15 15 0 0 1-.055-1.222V256.684a17.1 17.1 0 0 0-.91-5.176c-.598-1.649-1.422-3.215-2.496-4.606a15.9 15.9 0 0 0-3.863-3.554c-1.477-.965-3.094-1.703-4.77-2.13C39.168 214.153 2.824 138.59.297 30.419a12 12 0 0 1 .484-3.613c.34-1.168.852-2.305 1.531-3.325a11.7 11.7 0 0 1 2.41-2.73 12.5 12.5 0 0 1 3.098-1.906 10 10 0 0 1 1.133-.43c.399-.113.793-.227 1.192-.309a8 8 0 0 1 1.19-.171c.4-.032.825-.055 1.22-.055h88.496v.652h26.773a12 12 0 0 1 4.57.91A12.9 12.9 0 0 1 136.313 22a12 12 0 0 1 2.668 3.84c.622 1.45.993 2.984 1.051 4.547 1.274 44.234 10.078 68 30.914 80.625a12.1 12.1 0 0 0 4.371 1.535c1.532.226 3.094.14 4.625-.227a12.3 12.3 0 0 0 4.207-1.933 12.5 12.5 0 0 0 3.18-3.383v.027C230.938 42.328 308.727.11 399.891.11Zm-212.59 106.95.032-.032Zm0 0"/></svg>',
			'share' => true
		),
		'phone'      => array(
			'name'   => 'Phone',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="m493.4 24.6-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4"/></svg>',
			'follow' => true
		),
		'pinterest'  => array(
			'name'   => 'Pinterest',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M204 6.5C101.4 6.5 0 74.9 0 185.6 0 256 39.6 296 63.6 296c9.9 0 15.6-27.6 15.6-35.4 0-9.3-23.7-29.1-23.7-67.8 0-80.4 61.2-137.4 140.4-137.4 68.1 0 118.5 38.7 118.5 109.8 0 53.1-21.3 152.7-90.3 152.7-24.9 0-46.2-18-46.2-43.8 0-37.8 26.4-74.4 26.4-113.4 0-66.2-93.9-54.2-93.9 25.8 0 16.8 2.1 35.4 9.6 50.7-13.8 59.4-42 147.9-42 209.1 0 18.9 2.7 37.5 4.5 56.4 3.4 3.8 1.7 3.4 6.9 1.5 50.4-69 48.6-82.5 71.4-172.8 12.3 23.4 44.1 36 69.3 36 106.2 0 153.9-103.5 153.9-196.8C384 71.3 298.2 6.5 204 6.5"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'pocket'	 => array(
			'name'   => 'Pocket',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M407.6 64h-367C18.5 64 0 82.5 0 104.6v135.2C0 364.5 99.7 464 224.2 464c124 0 223.8-99.5 223.8-224.2V104.6c0-22.4-17.7-40.6-40.4-40.6m-162 268.5c-12.4 11.8-31.4 11.1-42.4 0C89.5 223.6 88.3 227.4 88.3 209.3c0-16.9 13.8-30.7 30.7-30.7 17 0 16.1 3.8 105.2 89.3 90.6-86.9 88.6-89.3 105.5-89.3s30.7 13.8 30.7 30.7c0 17.8-2.9 15.7-114.8 123.2"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'print'	     => array(
			'name'   => 'Print',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M448 192V77.25c0-8.49-3.37-16.62-9.37-22.63L393.37 9.37c-6-6-14.14-9.37-22.63-9.37H96C78.33 0 64 14.33 64 32v160c-35.35 0-64 28.65-64 64v112c0 8.84 7.16 16 16 16h48v96c0 17.67 14.33 32 32 32h320c17.67 0 32-14.33 32-32v-96h48c8.84 0 16-7.16 16-16V256c0-35.35-28.65-64-64-64m-64 256H128v-96h256zm0-224H128V64h192v48c0 8.84 7.16 16 16 16h48zm48 72c-13.25 0-24-10.75-24-24 0-13.26 10.75-24 24-24s24 10.74 24 24c0 13.25-10.75 24-24 24"/></svg>',
			'share'  => true
		),
		'rss'        => array(
			'name'   => 'RSS',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M128.081 415.959c0 35.369-28.672 64.041-64.041 64.041S0 451.328 0 415.959s28.672-64.041 64.041-64.041 64.04 28.673 64.04 64.041m175.66 47.25c-8.354-154.6-132.185-278.587-286.95-286.95C7.656 175.765 0 183.105 0 192.253v48.069c0 8.415 6.49 15.472 14.887 16.018 111.832 7.284 201.473 96.702 208.772 208.772.547 8.397 7.604 14.887 16.018 14.887h48.069c9.149.001 16.489-7.655 15.995-16.79m144.249.288C439.596 229.677 251.465 40.445 16.503 32.01 7.473 31.686 0 38.981 0 48.016v48.068c0 8.625 6.835 15.645 15.453 15.999 191.179 7.839 344.627 161.316 352.465 352.465.353 8.618 7.373 15.453 15.999 15.453h48.068c9.034-.001 16.329-7.474 16.005-16.504"/></svg>',
			'follow' => true
		),
		'reddit'	 => array(
			'name'   => 'Reddit',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M440.3 203.5c-15 0-28.2 6.2-37.9 15.9-35.7-24.7-83.8-40.6-137.1-42.3L293 52.3l88.2 19.8c0 21.6 17.6 39.2 39.2 39.2 22 0 39.7-18.1 39.7-39.7s-17.6-39.7-39.7-39.7c-15.4 0-28.7 9.3-35.3 22l-97.4-21.6c-4.9-1.3-9.7 2.2-11 7.1L246.3 177c-52.9 2.2-100.5 18.1-136.3 42.8-9.7-10.1-23.4-16.3-38.4-16.3-55.6 0-73.8 74.6-22.9 100.1-1.8 7.9-2.6 16.3-2.6 24.7 0 83.8 94.4 151.7 210.3 151.7 116.4 0 210.8-67.9 210.8-151.7 0-8.4-.9-17.2-3.1-25.1 49.9-25.6 31.5-99.7-23.8-99.7M129.4 308.9c0-22 17.6-39.7 39.7-39.7 21.6 0 39.2 17.6 39.2 39.7 0 21.6-17.6 39.2-39.2 39.2-22 .1-39.7-17.6-39.7-39.2m214.3 93.5c-36.4 36.4-139.1 36.4-175.5 0-4-3.5-4-9.7 0-13.7 3.5-3.5 9.7-3.5 13.2 0 27.8 28.5 120 29 149 0 3.5-3.5 9.7-3.5 13.2 0 4.1 4 4.1 10.2.1 13.7m-.8-54.2c-21.6 0-39.2-17.6-39.2-39.2 0-22 17.6-39.7 39.2-39.7 22 0 39.7 17.6 39.7 39.7-.1 21.5-17.7 39.2-39.7 39.2"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'skype'      => array(
			'name'   => 'Skype',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.7 299.8c2.9-14 4.7-28.9 4.7-43.8 0-113.5-91.9-205.3-205.3-205.3-14.9 0-29.7 1.7-43.8 4.7C161.3 40.7 137.7 32 112 32 50.2 32 0 82.2 0 144c0 25.7 8.7 49.3 23.3 68.2-2.9 14-4.7 28.9-4.7 43.8 0 113.5 91.9 205.3 205.3 205.3 14.9 0 29.7-1.7 43.8-4.7 19 14.6 42.6 23.3 68.2 23.3 61.8 0 112-50.2 112-112 .1-25.6-8.6-49.2-23.2-68.1m-194.6 91.5c-65.6 0-120.5-29.2-120.5-65 0-16 9-30.6 29.5-30.6 31.2 0 34.1 44.9 88.1 44.9 25.7 0 42.3-11.4 42.3-26.3 0-18.7-16-21.6-42-28-62.5-15.4-117.8-22-117.8-87.2 0-59.2 58.6-81.1 109.1-81.1 55.1 0 110.8 21.9 110.8 55.4 0 16.9-11.4 31.8-30.3 31.8-28.3 0-29.2-33.5-75-33.5-25.7 0-42 7-42 22.5 0 19.8 20.8 21.8 69.1 33 41.4 9.3 90.7 26.8 90.7 77.6 0 59.1-57.1 86.5-112 86.5"/></svg>',
			'follow' => true
		),
		'sms'        => array(
			'name'   => 'SMS',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32"/></svg>',
			'share'  => true
		),
		'snapchat'   => array(
			'name'   => 'Snapchat',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" stroke="#000" stroke-width="10" d="M510.846 392.673c-5.211 12.157-27.239 21.089-67.36 27.318-2.064 2.786-3.775 14.686-6.507 23.956-1.625 5.566-5.623 8.869-12.128 8.869l-.297-.005c-9.395 0-19.203-4.323-38.852-4.323-26.521 0-35.662 6.043-56.254 20.588-21.832 15.438-42.771 28.764-74.027 27.399-31.646 2.334-58.025-16.908-72.871-27.404-20.714-14.643-29.828-20.582-56.241-20.582-18.864 0-30.736 4.72-38.852 4.72-8.073 0-11.213-4.922-12.422-9.04-2.703-9.189-4.404-21.263-6.523-24.13-20.679-3.209-67.31-11.344-68.498-32.15a10.627 10.627 0 0 1 8.877-11.069c69.583-11.455 100.924-82.901 102.227-85.934.074-.176.155-.344.237-.515 3.713-7.537 4.544-13.849 2.463-18.753-5.05-11.896-26.872-16.164-36.053-19.796-23.715-9.366-27.015-20.128-25.612-27.504 2.437-12.836 21.725-20.735 33.002-15.453 8.919 4.181 16.843 6.297 23.547 6.297 5.022 0 8.212-1.204 9.96-2.171-2.043-35.936-7.101-87.29 5.687-115.969C158.122 21.304 229.705 15.42 250.826 15.42c.944 0 9.141-.089 10.11-.089 52.148 0 102.254 26.78 126.723 81.643 12.777 28.65 7.749 79.792 5.695 116.009 1.582.872 4.357 1.942 8.599 2.139 6.397-.286 13.815-2.389 22.069-6.257 6.085-2.846 14.406-2.461 20.48.058l.029.01c9.476 3.385 15.439 10.215 15.589 17.87.184 9.747-8.522 18.165-25.878 25.018-2.118.835-4.694 1.655-7.434 2.525-9.797 3.106-24.6 7.805-28.616 17.271-2.079 4.904-1.256 11.211 2.46 18.748q.13.254.239.515c1.301 3.03 32.615 74.46 102.23 85.934 6.427 1.058 11.163 7.877 7.725 15.859z"/></svg>',
			'follow' => true
		),
		'soundcloud' => array(
			'name'   => 'SoundCloud',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="m111.4 256.3 5.8 65-5.8 68.3c-.3 2.5-2.2 4.4-4.4 4.4s-4.2-1.9-4.2-4.4l-5.6-68.3 5.6-65c0-2.2 1.9-4.2 4.2-4.2 2.2 0 4.1 2 4.4 4.2m21.4-45.6c-2.8 0-4.7 2.2-5 5l-5 105.6 5 68.3c.3 2.8 2.2 5 5 5 2.5 0 4.7-2.2 4.7-5l5.8-68.3-5.8-105.6c0-2.8-2.2-5-4.7-5m25.5-24.1c-3.1 0-5.3 2.2-5.6 5.3l-4.4 130 4.4 67.8c.3 3.1 2.5 5.3 5.6 5.3 2.8 0 5.3-2.2 5.3-5.3l5.3-67.8-5.3-130c0-3.1-2.5-5.3-5.3-5.3M7.2 283.2c-1.4 0-2.2 1.1-2.5 2.5L0 321.3l4.7 35c.3 1.4 1.1 2.5 2.5 2.5s2.2-1.1 2.5-2.5l5.6-35-5.6-35.6c-.3-1.4-1.1-2.5-2.5-2.5m23.6-21.9c-1.4 0-2.5 1.1-2.5 2.5l-6.4 57.5 6.4 56.1c0 1.7 1.1 2.8 2.5 2.8s2.5-1.1 2.8-2.5l7.2-56.4-7.2-57.5c-.3-1.4-1.4-2.5-2.8-2.5m25.3-11.4c-1.7 0-3.1 1.4-3.3 3.3L47 321.3l5.8 65.8c.3 1.7 1.7 3.1 3.3 3.1 1.7 0 3.1-1.4 3.1-3.1l6.9-65.8-6.9-68.1c0-1.9-1.4-3.3-3.1-3.3m25.3-2.2c-1.9 0-3.6 1.4-3.6 3.6l-5.8 70 5.8 67.8c0 2.2 1.7 3.6 3.6 3.6s3.6-1.4 3.9-3.6l6.4-67.8-6.4-70c-.3-2.2-2-3.6-3.9-3.6m241.4-110.9c-1.1-.8-2.8-1.4-4.2-1.4-2.2 0-4.2.8-5.6 1.9-1.9 1.7-3.1 4.2-3.3 6.7v.8l-3.3 176.7 1.7 32.5 1.7 31.7c.3 4.7 4.2 8.6 8.9 8.6s8.6-3.9 8.6-8.6l3.9-64.2-3.9-177.5c-.4-3-2-5.8-4.5-7.2m-26.7 15.3c-1.4-.8-2.8-1.4-4.4-1.4s-3.1.6-4.4 1.4c-2.2 1.4-3.6 3.9-3.6 6.7l-.3 1.7-2.8 160.8s0 .3 3.1 65.6v.3c0 1.7.6 3.3 1.7 4.7 1.7 1.9 3.9 3.1 6.4 3.1 2.2 0 4.2-1.1 5.6-2.5 1.7-1.4 2.5-3.3 2.5-5.6l.3-6.7 3.1-58.6-3.3-162.8c-.3-2.8-1.7-5.3-3.9-6.7m-111.4 22.5c-3.1 0-5.8 2.8-5.8 6.1l-4.4 140.6 4.4 67.2c.3 3.3 2.8 5.8 5.8 5.8 3.3 0 5.8-2.5 6.1-5.8l5-67.2-5-140.6c-.2-3.3-2.7-6.1-6.1-6.1m376.7 62.8c-10.8 0-21.1 2.2-30.6 6.1-6.4-70.8-65.8-126.4-138.3-126.4-17.8 0-35 3.3-50.3 9.4-6.1 2.2-7.8 4.4-7.8 9.2v249.7c0 5 3.9 8.6 8.6 9.2h218.3c43.3 0 78.6-35 78.6-78.3.1-43.6-35.2-78.9-78.5-78.9m-296.7-60.3c-4.2 0-7.5 3.3-7.8 7.8l-3.3 136.7 3.3 65.6c.3 4.2 3.6 7.5 7.8 7.5s7.5-3.3 7.5-7.5l3.9-65.6-3.9-136.7c-.3-4.5-3.3-7.8-7.5-7.8m-53.6-7.8c-3.3 0-6.4 3.1-6.4 6.7l-3.9 145.3 3.9 66.9c.3 3.6 3.1 6.4 6.4 6.4 3.6 0 6.4-2.8 6.7-6.4l4.4-66.9-4.4-145.3c-.3-3.6-3.1-6.7-6.7-6.7m26.7 3.4c-3.9 0-6.9 3.1-6.9 6.9L227 321.3l3.9 66.4c.3 3.9 3.1 6.9 6.9 6.9s6.9-3.1 6.9-6.9l4.2-66.4-4.2-141.7c0-3.9-3-6.9-6.9-6.9"/></svg>',
			'follow' => true
		),
		'spotify'    => array(
			'name'   => 'Spotify',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><path fill="currentColor" d="M248 8C111.1 8 0 119.1 0 256s111.1 248 248 248 248-111.1 248-248S384.9 8 248 8m100.7 364.9c-4.2 0-6.8-1.3-10.7-3.6-62.4-37.6-135-39.2-206.7-24.5-3.9 1-9 2.6-11.9 2.6-9.7 0-15.8-7.7-15.8-15.8 0-10.3 6.1-15.2 13.6-16.8 81.9-18.1 165.6-16.5 237 26.2 6.1 3.9 9.7 7.4 9.7 16.5s-7.1 15.4-15.2 15.4m26.9-65.6c-5.2 0-8.7-2.3-12.3-4.2-62.5-37-155.7-51.9-238.6-29.4-4.8 1.3-7.4 2.6-11.9 2.6-10.7 0-19.4-8.7-19.4-19.4s5.2-17.8 15.5-20.7c27.8-7.8 56.2-13.6 97.8-13.6 64.9 0 127.6 16.1 177 45.5 8.1 4.8 11.3 11 11.3 19.7-.1 10.8-8.5 19.5-19.4 19.5m31-76.2c-5.2 0-8.4-1.3-12.9-3.9-71.2-42.5-198.5-52.7-280.9-29.7-3.6 1-8.1 2.6-12.9 2.6-13.2 0-23.3-10.3-23.3-23.6 0-13.6 8.4-21.3 17.4-23.9 35.2-10.3 74.6-15.2 117.5-15.2 73 0 149.5 15.2 205.4 47.8 7.8 4.5 12.9 10.7 12.9 22.6 0 13.6-11 23.3-23.2 23.3"/></svg>',
			'follow' => true
		),
		'stackoverflow' => array(
			'name'   => 'Stack Overflow',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M290.7 311 95 269.7 86.8 309l195.7 41zm51-87L188.2 95.7l-25.5 30.8 153.5 128.3zm-31.2 39.7L129.2 179l-16.7 36.5L293.7 300zM262 32l-32 24 119.3 160.3 32-24zm20.5 328h-200v39.7h200zm39.7 80H42.7V320h-40v160h359.5V320h-40z"/></svg>',
			'follow' => true
		),
		'steam'      => array(
			'name'   => 'Steam',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><path fill="currentColor" d="M496 256c0 137-111.2 248-248.4 248-113.8 0-209.6-76.3-239-180.4l95.2 39.3c6.4 32.1 34.9 56.4 68.9 56.4 39.2 0 71.9-32.4 70.2-73.5l84.5-60.2c52.1 1.3 95.8-40.9 95.8-93.5 0-51.6-42-93.5-93.7-93.5s-93.7 42-93.7 93.5v1.2L176.6 279c-15.5-.9-30.7 3.4-43.5 12.1L0 236.1C10.2 108.4 117.1 8 247.6 8 384.8 8 496 119 496 256M155.7 384.3l-30.5-12.6a52.8 52.8 0 0 0 27.2 25.8c26.9 11.2 57.8-1.6 69-28.4 5.4-13 5.5-27.3.1-40.3S206 305.6 193 300.2c-12.9-5.4-26.7-5.2-38.9-.6l31.5 13c19.8 8.2 29.2 30.9 20.9 50.7-8.3 19.9-31 29.2-50.8 21m173.8-129.9c-34.4 0-62.4-28-62.4-62.3s28-62.3 62.4-62.3 62.4 28 62.4 62.3-27.9 62.3-62.4 62.3m.1-15.6c25.9 0 46.9-21 46.9-46.8 0-25.9-21-46.8-46.9-46.8s-46.9 21-46.9 46.8c.1 25.8 21.1 46.8 46.9 46.8"/></svg>',
			'follow' => true
		),
		'subscribe'  => array(
			'name'   => __('Subscribe', 'novashare'),
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 417.1c-16.38 0-32.88-4.1-46.88-15.12L0 250.9V464c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-47.1v-214L302.9 402c-14 10-30.5 15.1-46.9 15.1M493.6 163c-8.8-7-17.2-13.5-29.6-22.9V95.98c0-26.5-21.5-48-48-48l-77.5.002c-3.125-2.25-5.875-4.25-9.125-6.5C312.6 29.13 279.3-.373 256 .002 232.8-.373 199.4 29.13 182.6 41.5c-3.25 2.25-6 4.25-9.125 6.5H96c-26.5 0-48 21.5-48 48v44.12C35.63 149.5 27.25 156 18.38 163 6.75 172 0 186 0 200.8v10.62l96 69.37V96h320v184.7l96-69.37V200.8c0-14.8-6.7-28.8-18.4-37.8M176 255.1h160c8.836 0 16-7.164 16-15.1 0-8.838-7.164-16-16-16H176c-8.836 0-16 7.162-16 16 0 8.8 7.2 15.1 16 15.1m0-64h160c8.836 0 16-7.164 16-16 0-8.838-7.164-15.1-16-15.1H176c-8.836 0-16 7.162-16 15.1 0 9.7 7.2 16 16 16"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'telegram'   => array(
			'name'   => 'Telegram',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="m446.7 98.6-67.6 318.8c-5.1 22.5-18.4 28.1-37.3 17.5l-103-75.9-49.7 47.8c-5.5 5.5-10.1 10.1-20.7 10.1l7.4-104.9 190.9-172.5c8.3-7.4-1.8-11.5-12.9-4.1L117.8 284 16.2 252.2c-22.1-6.9-22.5-22.1 4.6-32.7L418.2 66.4c18.4-6.9 34.5 4.1 28.5 32.2"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'threads'    => array(
			'name'   => 'Threads',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M331.5 235.7c2.2.9 4.2 1.9 6.3 2.8 29.2 14.1 50.6 35.2 61.8 61.4 15.7 36.5 17.2 95.8-30.3 143.2-36.2 36.2-80.3 52.5-142.6 53h-.3c-70.2-.5-124.1-24.1-160.4-70.2-32.3-41-48.9-98.1-49.5-169.6v-.5c.5-71.5 17.1-128.6 49.4-169.6 36.3-46.1 90.3-69.7 160.5-70.2h.3c70.3.5 124.9 24 162.3 69.9 18.4 22.7 32 50 40.6 81.7l-40.4 10.8c-7.1-25.8-17.8-47.8-32.2-65.4-29.2-35.8-73-54.2-130.5-54.6-57 .5-100.1 18.8-128.2 54.4C72.1 146.1 58.5 194.3 58 256c.5 61.7 14.1 109.9 40.3 143.3 28 35.6 71.2 53.9 128.2 54.4 51.4-.4 85.4-12.6 113.7-40.9 32.3-32.2 31.7-71.8 21.4-95.9-6.1-14.2-17.1-26-31.9-34.9-3.7 26.9-11.8 48.3-24.7 64.8-17.1 21.8-41.4 33.6-72.7 35.3-23.6 1.3-46.3-4.4-63.9-16-20.8-13.8-33-34.8-34.3-59.3-2.5-48.3 35.7-83 95.2-86.4 21.1-1.2 40.9-.3 59.2 2.8-2.4-14.8-7.3-26.6-14.6-35.2-10-11.7-25.6-17.7-46.2-17.8h-.7c-16.6 0-39 4.6-53.3 26.3l-34.4-23.6c19.2-29.1 50.3-45.1 87.8-45.1h.8c62.6.4 99.9 39.5 103.7 107.7l-.2.2zm-156 68.8c1.3 25.1 28.4 36.8 54.6 35.3 25.6-1.4 54.6-11.4 59.5-73.2-13.2-2.9-27.8-4.4-43.4-4.4-4.8 0-9.6.1-14.4.4-42.9 2.4-57.2 23.2-56.2 41.8z"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'tiktok'	 => array(
			'name'   => 'TikTok',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M448 209.91a210.06 210.06 0 0 1-122.77-39.25v178.72A162.55 162.55 0 1 1 185 188.31v89.89a74.62 74.62 0 1 0 52.23 71.18V0h88a121 121 0 0 0 1.86 22.17A122.18 122.18 0 0 0 381 102.39a121.43 121.43 0 0 0 67 20.14Z"/></svg>',
			'follow' => true
		),
		'tripadvisor' => array(
			'name'   => 'Tripadvisor',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M528.91 178.82 576 127.58H471.66a326.11 326.11 0 0 0-367 0H0l47.09 51.24a143.911 143.911 0 0 0 194.77 211.91l46.14 50.2 46.11-50.17a143.94 143.94 0 0 0 241.77-105.58h-.03a143.56 143.56 0 0 0-46.94-106.36M144.06 382.57a97.39 97.39 0 1 1 97.39-97.39 97.39 97.39 0 0 1-97.39 97.39M288 282.37c0-64.09-46.62-119.08-108.09-142.59a281 281 0 0 1 216.17 0C334.61 163.3 288 218.29 288 282.37m143.88 100.2h-.01a97.405 97.405 0 1 1 .01 0M144.06 234.12h-.01a51.06 51.06 0 1 0 51.06 51.06v-.11a51 51 0 0 0-51.05-50.95m287.82 0a51.06 51.06 0 1 0 51.06 51.06 51.06 51.06 0 0 0-51.06-51.06"/></svg>',
			'follow' => true
		),
		'tumblr'		=> array(
			'name' => 'Tumblr',
			'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M309.8 480.3c-13.6 14.5-50 31.7-97.4 31.7-120.8 0-147-88.8-147-140.6v-144H17.9c-5.5 0-10-4.5-10-10v-68c0-7.2 4.5-13.6 11.3-16 62-21.8 81.5-76 84.3-117.1.8-11 6.5-16.3 16.1-16.3h70.9c5.5 0 10 4.5 10 10v115.2h83c5.5 0 10 4.4 10 9.9v81.7c0 5.5-4.5 10-10 10h-83.4V360c0 34.2 23.7 53.6 68 35.8 4.8-1.9 9-3.2 12.7-2.2 3.5.9 5.8 3.4 7.4 7.9l22 64.3c1.8 5 3.3 10.6-.4 14.5"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'twitter'    => array(
			'name'   => 'X',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8l164.9-188.5L26.8 48h145.6l100.5 132.9zm-24.8 373.8h39.1L151.1 88h-42z"/></svg>',
			'share'  => true,
			'follow' => true,
			'highlight' => true
		),
		'vkontakte'		=> array(
			'name' => 'VK',
			'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M545 117.7c3.7-12.5 0-21.7-17.8-21.7h-58.9c-15 0-21.9 7.9-25.6 16.7 0 0-30 73.1-72.4 120.5-13.7 13.7-20 18.1-27.5 18.1-3.7 0-9.4-4.4-9.4-16.9V117.7c0-15-4.2-21.7-16.6-21.7h-92.6c-9.4 0-15 7-15 13.5 0 14.2 21.2 17.5 23.4 57.5v86.8c0 19-3.4 22.5-10.9 22.5-20 0-68.6-73.4-97.4-157.4-5.8-16.3-11.5-22.9-26.6-22.9H38.8c-16.8 0-20.2 7.9-20.2 16.7 0 15.6 20 93.1 93.1 195.5C160.4 378.1 229 416 291.4 416c37.5 0 42.1-8.4 42.1-22.9 0-66.8-3.4-73.1 15.4-73.1 8.7 0 23.7 4.4 58.7 38.1 40 40 46.6 57.9 69 57.9h58.9c16.8 0 25.3-8.4 20.4-25-11.2-34.9-86.9-106.7-90.3-111.5-8.7-11.2-6.2-16.2 0-26.2.1-.1 72-101.3 79.4-135.6"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'vimeo'      => array(
			'name'   => 'Vimeo',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M447.8 153.6c-2 43.6-32.4 103.3-91.4 179.1-60.9 79.2-112.4 118.8-154.6 118.8q-39.15 0-66.3-72.3C100.3 250 85.3 174.3 56.2 174.3c-3.4 0-15.1 7.1-35.2 21.1L0 168.2c51.6-45.3 100.9-95.7 131.8-98.5 34.9-3.4 56.3 20.5 64.4 71.5 28.7 181.5 41.4 208.9 93.6 126.7 18.7-29.6 28.8-52.1 30.2-67.6 4.8-45.9-35.8-42.8-63.3-31 22-72.1 64.1-107.1 126.2-105.1 45.8 1.2 67.5 31.1 64.9 89.4"/></svg>',
			'follow' => true
		),
		'whatsapp'   => array(
			'name'   => 'WhatsApp',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157m-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1s56.2 81.2 56.1 130.5c0 101.8-84.9 184.6-186.6 184.6m101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8s-14.3 18-17.6 21.8c-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7s-12.5-30.1-17.1-41.2c-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2s-9.7 1.4-14.8 6.9c-5.1 5.6-19.4 19-19.4 46.3s19.9 53.7 22.6 57.4c2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4s4.6-24.1 3.2-26.4c-1.3-2.5-5-3.9-10.5-6.6"/></svg>',
			'share'  => true,
			'highlight' => true
		),
		'wordpress'  => array(
			'name'   => 'WordPress',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="m61.7 169.4 101.5 278C92.2 413 43.3 340.2 43.3 256c0-30.9 6.6-60.1 18.4-86.6m337.9 75.9c0-26.3-9.4-44.5-17.5-58.7-10.8-17.5-20.9-32.4-20.9-49.9 0-19.6 14.8-37.8 35.7-37.8.9 0 1.8.1 2.8.2-37.9-34.7-88.3-55.9-143.7-55.9-74.3 0-139.7 38.1-177.8 95.9 5 .2 9.7.3 13.7.3 22.2 0 56.7-2.7 56.7-2.7 11.5-.7 12.8 16.2 1.4 17.5 0 0-11.5 1.3-24.3 2l77.5 230.4L249.8 247l-33.1-90.8c-11.5-.7-22.3-2-22.3-2-11.5-.7-10.1-18.2 1.3-17.5 0 0 35.1 2.7 56 2.7 22.2 0 56.7-2.7 56.7-2.7 11.5-.7 12.8 16.2 1.4 17.5 0 0-11.5 1.3-24.3 2l76.9 228.7 21.2-70.9c9-29.4 16-50.5 16-68.7m-139.9 29.3-63.8 185.5c19.1 5.6 39.2 8.7 60.1 8.7 24.8 0 48.5-4.3 70.6-12.1-.6-.9-1.1-1.9-1.5-2.9zm183-120.7c.9 6.8 1.4 14 1.4 21.9 0 21.6-4 45.8-16.2 76.2l-65 187.9C426.2 403 468.7 334.5 468.7 256c0-37-9.4-71.8-26-102.1M504 256c0 136.8-111.3 248-248 248C119.2 504 8 392.7 8 256 8 119.2 119.2 8 256 8c136.7 0 248 111.2 248 248m-11.4 0c0-130.5-106.2-236.6-236.6-236.6C125.5 19.4 19.4 125.5 19.4 256S125.6 492.6 256 492.6c130.5 0 236.6-106.1 236.6-236.6"/></svg>',
			'follow' => true
		),
		'xing'		 => array(
			'name'   => 'Xing',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M162.7 210c-1.8 3.3-25.2 44.4-70.1 123.5-4.9 8.3-10.8 12.5-17.7 12.5H9.8c-7.7 0-12.1-7.5-8.5-14.4l69-121.3q.3 0 0-.3l-43.9-75.6c-4.3-7.8.3-14.1 8.5-14.1H100c7.3 0 13.3 4.1 18 12.2zM382.6 46.1l-144 253v.3L330.2 466c3.9 7.1.2 14.1-8.5 14.1h-65.2c-7.6 0-13.6-4-18-12.2l-92.4-168.5c3.3-5.8 51.5-90.8 144.8-255.2 4.6-8.1 10.4-12.2 17.5-12.2h65.7c8 0 12.3 6.7 8.5 14.1"/></svg>',
			'share'  => true,
			'follow' => true
		),
		'yelp' => array(
			'name'   => 'Yelp',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="m42.9 240.32 99.62 48.61c19.2 9.4 16.2 37.51-4.5 42.71L30.5 358.45a22.79 22.79 0 0 1-28.21-19.6 197.2 197.2 0 0 1 9-85.32 22.8 22.8 0 0 1 31.61-13.21m44 239.25a199.45 199.45 0 0 0 79.42 32.11A22.78 22.78 0 0 0 192.94 490l3.9-110.82c.7-21.3-25.5-31.91-39.81-16.1l-74.21 82.4a22.82 22.82 0 0 0 4.09 34.09zm145.34-109.92 58.81 94a22.93 22.93 0 0 0 34 5.5 198.4 198.4 0 0 0 52.71-67.61A23 23 0 0 0 364.17 370l-105.42-34.26c-20.31-6.5-37.81 15.8-26.51 33.91m148.33-132.23a197.4 197.4 0 0 0-50.41-69.31 22.85 22.85 0 0 0-34 4.4l-62 91.92c-11.9 17.7 4.7 40.61 25.2 34.71L366 268.63a23 23 0 0 0 14.61-31.21zM62.11 30.18a22.86 22.86 0 0 0-9.9 32l104.12 180.44c11.7 20.2 42.61 11.9 42.61-11.4V22.88a22.67 22.67 0 0 0-24.5-22.8 320.4 320.4 0 0 0-112.33 30.1"/></svg>',
			'follow' => true
		),
		'youtube'    => array(
			'name'   => 'YouTube',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path fill="currentColor" d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305m-317.51 213.508V175.185l142.739 81.205z"/></svg>',
			'follow' => true
		),
		'yummly'        => array(
			'name' => 'Yummly',
			'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -2 30 22"><g transform="matrix(1,0,0,1,-1.471,-8.23102)"><g transform="matrix(0.0296871,0,0,-0.0296871,1.20836,26.4492)"><path fill="currentColor" d="M304,601C322,614 365,619 376,599C383,588 379,565 376,547C357,436 333,309 315,205C414,195 517,173 622,157C741,140 883,133 968,191C984,179 1000,164 999,139C997,109 952,93 923,83C887,72 849,66 808,66C626,67 475,137 307,146C289,65 252,-33 149,-28C104,-26 67,2 64,50C61,98 91,137 120,159C153,185 193,197 241,203C243,221 248,237 250,255C242,257 234,251 226,247C194,232 127,211 86,235C27,270 72,367 86,421C91,440 96,461 101,480C106,498 115,520 103,537C82,542 64,531 51,520C46,517 45,509 39,512C25,523 7,536 9,559C10,582 50,599 71,606C136,628 193,603 186,527C183,494 169,456 160,424C151,389 137,356 142,318C157,300 187,309 208,314C230,321 252,329 265,340C273,377 278,413 285,451C294,499 308,544 304,601ZM154,50C211,41 218,97 229,139C179,138 124,90 154,50ZM734,475C723,498 676,497 658,481C665,452 657,431 651,400C648,382 646,362 643,343C636,305 620,257 629,233C637,214 687,213 702,230C698,290 719,352 727,412C739,417 761,434 776,422C791,410 774,353 769,326C764,292 749,246 762,228C772,216 813,213 833,228C827,292 851,351 857,412C870,417 891,435 906,422C914,415 911,392 907,373C904,355 902,341 899,324C893,290 878,243 894,227C905,215 944,214 963,228C960,275 968,311 977,356C983,390 993,423 990,449C983,520 894,495 855,469C837,512 764,494 734,475ZM466,387C471,419 483,455 476,476C468,499 419,496 401,481C408,446 398,418 391,378C387,351 380,323 378,299C371,208 450,212 506,242C512,234 512,227 523,222C542,213 573,219 585,230C579,280 590,317 601,373C606,404 625,461 611,481C598,498 554,496 540,480C545,421 523,362 516,304C502,296 473,286 457,299C451,322 460,353 466,387Z" /></g></g></svg>',
			'share'  => true
		),
		'share'        => array(
			'name' => 'Share',
			'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M352 320c-22.608 0-43.387 7.819-59.79 20.895l-102.486-64.054a96.55 96.55 0 0 0 0-41.683l102.486-64.054C308.613 184.181 329.392 192 352 192c53.019 0 96-42.981 96-96S405.019 0 352 0s-96 42.981-96 96c0 7.158.79 14.13 2.276 20.841L155.79 180.895C139.387 167.819 118.608 160 96 160c-53.019 0-96 42.981-96 96s42.981 96 96 96c22.608 0 43.387-7.819 59.79-20.895l102.486 64.054A96.3 96.3 0 0 0 256 416c0 53.019 42.981 96 96 96s96-42.981 96-96-42.981-96-96-96"/></svg>',
			'share'  => true
		),
		'custom'   => array(
			'name' => 'Custom',
			'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path fill="currentColor" d="M579.8 267.7c56.5-56.5 56.5-148 0-204.5-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6 31.5 31.5 31.5 82.5 0 114L422.3 334.8c-31.5 31.5-82.5 31.5-114 0-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C206.5 251.2 213 330 263 380c56.5 56.5 148 56.5 204.5 0zM60.2 244.3c-56.5 56.5-56.5 148 0 204.5 50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C74 372 74 321 105.5 289.5l112.2-112.3c31.5-31.5 82.5-31.5 114 0 27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C433.5 260.8 427 182 377 132c-56.5-56.5-148-56.5-204.5 0z"/></svg>',
			'follow'  => true
		)
	);
	
	//filter entire networks array
	$networks = apply_filters('novashare_networks', $networks);

	//filter out networks not matching requested type
	$filtered_networks = array_filter($networks, function($a) use ($type) {
		return !empty($a[$type]);
	});

	//return networks array
	return $filtered_networks;
}

//return current active networks
function novashare_active_networks() {

	//get options
	$novashare = get_option('novashare');
	
	//locations and their network options to loop through
	$locations = array('inline' => 'social_networks', 'floating' => 'social_networks');

	//build active networks
	$networks  = array();
	foreach($locations as $location => $option) {
		if(!empty($novashare[$location]['enabled']) && !empty($novashare[$location][$option])) {
			foreach($novashare[$location][$option] as $key => $network) {
				if(!in_array($network, $networks)) {
					$networks[] = $network;
				}
			}
		}
	}

	//return active networks
	return $networks;
}

//return network share link
function novashare_network_link($network, $location = '') {

	if(empty($network)) {
		return '';
	}

	//global $wpdb;
	$post = novashare_get_global_post();

	$novashare = get_option('novashare');

	$shorten = true;

	//make sure we have valid post
	if($post && (is_singular() || in_the_loop()) && in_array($post->post_status, array('publish', 'private', 'inherit'))) {

		//post permalink
		$permalink = apply_filters('novashare_post_permalink', get_the_permalink($post->ID));

		//get existing details row
		$details = novashare_get_post_details($post->ID);

		//title
		$title = !empty($details['social_title']) ? $details['social_title'] : $post->post_title;

		//image
		$social_image = '';

		if(!empty($details['social_image'])) {
			$social_image = wp_get_attachment_url($details['social_image']);
		}
		if(empty($social_image)) {
			$social_image = get_the_post_thumbnail_url($post->ID, 'full');
		}
		if(empty($social_image) && !empty($novashare['default_social_image'])) {
			$social_image = wp_get_attachment_url($novashare['default_social_image']);
		}
	}
	else {

		if(is_home()) {
			$title = get_bloginfo('name');
			$permalink = get_post_type_archive_link('post');
		}
		elseif(is_archive()) {
			$title = strip_tags(get_the_archive_title());
			$permalink = get_pagenum_link();
			$shorten = false;
		}
		else {
			return '';
		}

		$social_image = '';
	}

	//add Google UTM tracking
	if(!empty($novashare['google_utm']) && !empty($novashare['google_utm_source']) && !empty($novashare['google_utm_medium']) && !empty($novashare['google_utm_name'])) {
		$google_utm = array(
			'utm_source' => trim(str_replace('{{network}}', $network, $novashare['google_utm_source'])),
			'utm_medium' => trim(str_replace('{{network}}', $network, $novashare['google_utm_medium'])),
			'utm_campaign' => trim(str_replace('{{network}}', $network, $novashare['google_utm_name']))
		);
		$permalink = add_query_arg(apply_filters('novashare_google_utm', $google_utm, $network), $permalink);
	}

	//bitly link shortening
	if(!empty($novashare['enable_bitly']) && !in_array($network, array('linkedin', 'pinterest', 'hackernews')) && $shorten) {
		$permalink = novashare_bitly_link($permalink, $network);
	}

	//encode final permalink
	$permalink = rawurlencode(apply_filters('novashare_permalink', $permalink, $network));

	//apply filters
	$title = rawurlencode(apply_filters('novashare_meta_title', $title, $network));

	if($location == 'highlight') {
		$title = '%highlight%';
	}

	//build link for network
	switch($network) {
		case 'twitter':
			$link = "https://x.com/intent/tweet?text=" . $title . "&url=" . $permalink . (!empty($novashare['twitter_username']) ? "&via=" . $novashare['twitter_username'] : "");
			break;

		case 'facebook':
			$link = "https://www.facebook.com/sharer/sharer.php?u=" . $permalink;
			break;

		case 'linkedin':
			$link = "https://www.linkedin.com/shareArticle?title=" . $title . "&url=" . $permalink . "&mini=true";
			break;

		case 'pinterest':
			$pinterest_image = '';
			if(!empty($details['pinterest_image'])) {
				$pinterest_image = wp_get_attachment_url($details['pinterest_image']);
			}
			if(empty($pinterest_image)) {
				$pinterest_image = $social_image;
			}
			$pinterest_description = !empty($details['pinterest_description']) ? rawurlencode($details['pinterest_description']) : $title;
			if($pinterest_image) {
				$link = "https://pinterest.com/pin/create/button/?url=" . $permalink . "&media=" . $pinterest_image . "&description=" . $pinterest_description;
			}
			else {
				$link = '#';
			}
			break;

		case 'buffer':
			$link = "https://buffer.com/add?url=" . $permalink . "&text=" . $title;
			break;

		case 'reddit':
			$link = "https://www.reddit.com/submit?url=" . $permalink . "&title=" . $title;
			break;

		case 'hackernews':
			$link = "https://news.ycombinator.com/submitlink?u=" . $permalink . "&t=" . $title;
			break;

		case 'nextdoor':
			$link = "https://nextdoor.com/sharekit/?source={website}&body=" . $title . "%20" . $permalink;
			break;

		case 'pocket':	
			$link = "https://getpocket.com/edit?url=" . $permalink;
			break;

		case 'whatsapp':
			$link = "https://api.whatsapp.com/send?text=" . $title . "+" . $permalink;
			break;

		case 'tumblr':
			$link = "https://www.tumblr.com/widgets/share/tool?canonicalUrl=" . $permalink;
			break;

		case 'vkontakte':
			$link = "https://vk.com/share.php?url=" . $permalink;
			break;

		case 'xing':
			$link = "https://www.xing.com/spi/shares/new?url=" . $permalink;
			break;

		case 'flipboard':
			$link = "https://share.flipboard.com/bookmarklet/popout?v=2&url=" . $permalink . "&title=" . $title;
			break;

		case 'telegram':
			$link = "https://telegram.me/share/url?url=" . $permalink . "&text=" . $title;
			break;

		case 'mix':
			$link = "https://mix.com/add?url=" . $permalink;
			break;

		case 'threads':
			$link = 'https://www.threads.net/intent/post?text=' . $permalink;
			break;

		case 'yummly':
			$link = "https://www.yummly.com/urb/verify?url=" . $permalink . "&title=" . $title . "&image=" . $social_image . "&yumtype=button";
			break;

		case 'sms':
			$link = "sms:?&body=" . $title . "%20" . $permalink;
			break;

		case 'mastodon':
			$link = 'https://mastodon.social/share?text=' . $permalink;
			if(!empty($novashare['mastodon_username'])) {
				$link.= rawurlencode(' via @' . $novashare['mastodon_username']);
			}
			$link.= '&title=' . $title;
			break;

		case 'bluesky':
			$link = "https://bsky.app/intent/compose?text=" . $title . " " . $permalink;
			break;

		case 'messenger':
			if(!wp_is_mobile()) {
				if(!empty($novashare['facebook_app_id'])) {
					$link = "https://www.facebook.com/dialog/send?app_id=" . $novashare['facebook_app_id'] . "&display=popup&link=" . $permalink . "&redirect_uri=" . $permalink;
				}
				else {
					$link = "https://www.facebook.com/sharer/sharer.php?u=" . $permalink;
				}
			}
			else {
				$link = "fb-messenger://share/?link=" . $permalink;
			}
			break;

		case 'line':
			$link = "https://lineit.line.me/share/ui?url=" . $permalink . "&text=" . $title;
			break;

		case 'email':
			$link = "mailto:?subject=" . esc_attr($title) . "&amp;body=" . $permalink;
			break;

		case 'subscribe':
			$link = $novashare['subscribe_link'] ?? '';
			break;

		default:
			$link = '#';
			break;
	}

	//return final link
	return apply_filters('novashare_network_link', $link, $network);
}

//display inline content
function novashare_inline_content($content, $direct = false) {

	global $wp_current_filter;
	$novashare = get_option('novashare');

	//bail if the_content is being requested by something else
    if(!empty($wp_current_filter) && is_array($wp_current_filter)) {
    	if(count(array_intersect($wp_current_filter, apply_filters('novashare_inline_excluded_filters', array('wp_head', 'get_the_excerpt', 'widget_text_content', 'p3_content_end')))) > 0) {
	     	return $content;
		}

		//nested the_content hook
		if(empty($novashare['inline']['feeds']) || !in_the_loop()) {
			$filter_counts = array_count_values($wp_current_filter);
			if(!empty($filter_counts['the_content']) && $filter_counts['the_content'] > 1) {
				return $content;
			}
		}
    }

	if(!is_singular() && (empty($novashare['inline']['feeds']) || !in_the_loop())) {
		return $content;
	}

	if(!empty($novashare['inline']['position']) && $novashare['inline']['position'] == 'neither' && $direct == false) {
		return $content;
	}

	if(!empty(apply_filters('novashare_inline', !empty($novashare['inline']['enabled'])))) {

		$post = novashare_get_global_post();

		if($post && ((!empty($novashare['inline']['post_types']) && in_array($post->post_type, $novashare['inline']['post_types'])) || $direct === true)) {

			//get post details row
			$details = novashare_get_post_details($post->ID);

			if(empty($details['hide_inline'])) {

				global $novashare_inline_print;

				if(!$direct && $novashare_inline_print && is_singular() && !in_the_loop()) {
					return $content;
				}

				$output = "";

				global $novashare_inline_css;

				//inline styles
				if(!is_array($novashare_inline_css) || !in_array('inline', $novashare_inline_css)) {
					$output.= novashare_print_inline_styles('inline', $novashare['inline']);
				}

				$output.= novashare_print_buttons('inline');

				//add output to content
				if(!empty($output)) {
					$inline_position = apply_filters('novashare_inline_position', $novashare['inline']['position']);
					if(!empty($inline_position) && $direct == false) {

						//add below content class
						$below_output = str_replace('ns-buttons ns-inline', 'ns-buttons ns-inline ns-inline-below', $output);

						if($inline_position == 'below') {
							$content = $content . $below_output;
						}
						elseif($inline_position == 'both') {
							$content = $output . $content . $below_output;
						}
					}
					else {
						$content = $output . $content;
					}

					$novashare_inline_print = true;
				}
			}
		}
	}

	return $content;
}
add_filter('the_content', 'novashare_inline_content', 25);

//inline content shortcode
function novashare_inline_content_shortcode() {

	$inline_buttons = novashare_inline_content('', true);
	
	if(!empty($inline_buttons)) {
		return $inline_buttons;
	}
}
add_shortcode('novashare_inline_content', 'novashare_inline_content_shortcode');

//display floating bar
function novashare_floating_bar($direct = false) {

	if(!novashare_is_floating_allowed()) {
		return;
	}

	$novashare = get_option('novashare');

	if(!empty($novashare['floating']['position']) && $novashare['floating']['position'] == 'none' && $direct == false) {
		return;
	}

	$output = "";

	global $novashare_inline_css;

	//inline floating styles
	if(!is_array($novashare_inline_css) || !in_array('floating', $novashare_inline_css)) {

		$button_margin = !empty($novashare['floating']['button_margin']) ? intval($novashare['floating']['button_margin']) : 10;
		$container_offset = !empty($novashare['floating']['container_offset']) ? intval($novashare['floating']['container_offset']) : 5;
		$top_offset = !empty($novashare['floating']['top_offset']) && $novashare['floating'] != '25%' ? $novashare['floating']['top_offset'] : '';

		$output.= "<style>";
			if(!empty($novashare['floating']['alignment']) && $novashare['floating']['alignment'] == 'right') {
				$output.= ".ns-floating {
					left: unset;
					right: " . $container_offset . "px;
					" . (!empty($top_offset) ? "top: " . $top_offset . ";" : "") . "
				}
				.ns-floating .ns-buttons-wrapper {
					align-content: flex-start;
					flex-wrap: wrap-reverse;
				}
				.ns-floating a.ns-button, .ns-floating .ns-total-share-count {
					margin: 0px 0px " . $button_margin . "px " . $button_margin . "px;
				}";
			}
			else {
				$temp_output = "";
				if($container_offset != 5) {
					$temp_output.= "left: " . $container_offset . "px;";
				}
				if(!empty($top_offset)) {
					$temp_output.= "top: " . $top_offset . ";";
				}
				if(!empty($temp_output)) {
					$output.= ".ns-floating{" . $temp_output . "}";
				}
				if($button_margin != 10) {
					$output.= ".ns-floating a.ns-button, .ns-floating .ns-total-share-count {
						margin: 0px " . $button_margin . "px " . $button_margin . "px 0px;
					}";
				}
			}

			//above breakpoint styles
			$above_breakpoint_styles = '';
			if(!empty($novashare['floating']['hide_above_breakpoint'])) {
				$above_breakpoint_styles.= "
				.ns-floating {
					display: none;
				}";
			}
			elseif(!empty($novashare['floating']['show_on_scroll']) && (empty($novashare['floating']['show_on_scroll_location']) || $novashare['floating']['show_on_scroll_location'] == 'desktop')) {
				$above_breakpoint_styles.= "
				.ns-floating {
					visibility: hidden;
				}";
			}

			if(!empty($above_breakpoint_styles)) {
				$output.= "@media (min-width: " . ((int)(!empty($novashare['floating']['breakpoint']) ? $novashare['floating']['breakpoint'] : "1200") + 1) . "px) {
					" . $above_breakpoint_styles . "
				}";
			}

			//below breakpoint styles
			$output.= "@media (max-width: " . (!empty($novashare['floating']['breakpoint']) ? $novashare['floating']['breakpoint'] : "1200px") . ") {";
			if(!empty($novashare['floating']['hide_below_breakpoint'])) {
				$output.= ".ns-floating { 
					display:none;
				}";
			}
			else {
				$background_padding = !empty($novashare['floating']['background_padding']) ? intval($novashare['floating']['background_padding']) : 10;
				$button_height = !empty($novashare['floating']['size']) ? ($novashare['floating']['size'] == 'small' ? 30 : 50) : 40;
				$output.= "
				body {
					padding-bottom: " . ((($background_padding>$button_margin?$background_padding:$button_margin)*2)+$button_height) . "px;
				}
				.ns-floating {
					" . (!empty($novashare['floating']['show_on_scroll']) && (empty($novashare['floating']['show_on_scroll_location']) || $novashare['floating']['show_on_scroll_location'] == 'mobile') ? "visibility: hidden;" : "") . "
					background-color: " . (!empty($novashare['floating']['background_color']) ? $novashare['floating']['background_color'] : "#fff") . ";
					padding: " . ($background_padding-$button_margin < 0 ? 0 : $background_padding-$button_margin) . "px " . $background_padding . "px " . ($button_margin-$background_padding > 0 ? $background_padding+($button_margin-$background_padding) : $background_padding) . "px " . $background_padding . "px;
					top: auto;
	  				bottom: 0;
	  				left: 0;
	  				right: 0;
	  				height: auto;
				}
				.ns-floating .ns-buttons-wrapper {
					flex-direction: row;
					justify-content: center;
				}
				.ns-floating a.ns-button, .ns-floating .ns-total-share-count {
					margin: " . $button_margin . "px " . $button_margin/2 . "px 0px " . $button_margin/2 . "px;
				}";
				if(!empty($novashare['floating']['fill_space'])) {
					$output.= ".ns-floating a.ns-button {
						flex-grow: 1;
					}";
				} 
				if(!empty($novashare['floating']['hide_total_share_count'])) {
					$output.= ".ns-floating .ns-total-share-count {
						display: none;
					}";
				}
			}
			$output.= "}";

			if(!empty($novashare['floating']['mobile_max_width'])) {
				$output.= "@media (min-width: " . ((int)$novashare['floating']['mobile_max_width'] + 1) . "px) and (max-width: " . (!empty($novashare['floating']['breakpoint']) ? $novashare['floating']['breakpoint'] : "1200px") . ") {
					body {
						padding-bottom: 0px;
					}
					.ns-floating {
						display: none;
					}
				}";
			}
		$output.= "</style>";
	}

	//add buttons to output
	$output.= novashare_print_buttons('floating');

	//show on scroll javascript
	if(!empty($novashare['floating']['show_on_scroll'])) {

		$threshold = !empty($novashare['floating']['scroll_threshold']) ? ((strpos($novashare['floating']['scroll_threshold'], '%') !== false ? "h*." : "") . intval($novashare['floating']['scroll_threshold'])) : 0;

		$output.= "<script>var f=document.querySelector('.ns-floating');function novashareScroll(){var h=document.body.scrollHeight;";

			if(empty($novashare['floating']['show_on_scroll_location'])) {
				$output.= "novashareScrollDisplay(" . $threshold . ")";
			}
			else {
				$output.= "window.innerWidth>" . (!empty($novashare['floating']['breakpoint']) ? intval($novashare['floating']['breakpoint']) : 1200) . "?" . (empty($novashare['floating']['hide_above_breakpoint']) && (empty($novashare['floating']['show_on_scroll_location']) || $novashare['floating']['show_on_scroll_location'] == 'desktop') ? "novashareScrollDisplay(" . $threshold . ")" : "f.style.visibility='visible'") . ":" . (empty($novashare['floating']['hide_below_breakpoint']) && (empty($novashare['floating']['show_on_scroll_location']) || $novashare['floating']['show_on_scroll_location'] == 'mobile') ? "novashareScrollDisplay(" . $threshold . ");" : "f.style.visibility='visible';");
			}

			$output.= "};function novashareScrollDisplay(t){f.style.visibility=window.scrollY>t?'visible':'hidden';}window.addEventListener('load',novashareScroll);window.addEventListener('scroll',novashareScroll);</script>";
	}

	//print output
	if(!$direct) {
		echo $output;
	}
	else {
		return $output;
	}
	
}
add_filter('wp_footer', 'novashare_floating_bar', 25);

//floating bar shortcode
function novashare_floating_bar_shortcode() {

	$floating_buttons = novashare_floating_bar(true);
	
	if(!empty($floating_buttons)) {
		return $floating_buttons;
	}
}
add_shortcode('novashare_floating', 'novashare_floating_bar_shortcode');

//check if floating bar is allowed to display for post
function novashare_is_floating_allowed() {

	$novashare = get_option('novashare');

	if(!empty(apply_filters('novashare_floating', !empty($novashare['floating']['enabled'])))) {

		$post = novashare_get_global_post();

		if(is_singular() && isset($post) && !empty($post->ID) && !empty($post->post_type)) {
			if((!empty($novashare['floating']['post_types']) && in_array($post->post_type, $novashare['floating']['post_types'])) || (!empty($novashare['floating']['home_page']) && is_front_page())) {

				$details = novashare_get_post_details($post->ID);

				if(empty($details['hide_floating'])) {
					return true;
				}
			}
		}

		if(is_home() && !empty($novashare['floating']['posts_page'])) {
			return true;
		}

		if(is_archive() && !empty($novashare['floating']['archives'])) {
			return true;
		}
	}

	return false;
}

//print social network buttons
function novashare_print_buttons($location, $params = array()) {

	global $wpdb;

	$post = novashare_get_global_post();

	$novashare = get_option('novashare');

	if($location == 'share') {
		$novashare['share']['labels'] = 1;
		$novashare['share']['layout'] = '3-col';
		if(empty($novashare['share']['social_networks'])) {
			$novashare['share']['social_networks'] = array_keys(novashare_networks('share'));
			if(($key = array_search('share', $novashare['share']['social_networks'])) !== false) {
			    unset($novashare['share']['social_networks'][$key]);
			}
		}
		unset($novashare['share']['cta_text']);
	}

	if(!empty($params)) {
		$novashare[$location] = array_merge($novashare[$location] ?? array(), $params);
	}

	//get post share counts
	if(!empty($novashare[$location]['total_share_count']) || !empty($novashare[$location]['network_share_counts'])) {

		$post_id = is_singular() || ($post && in_the_loop()) ? $post->ID : (is_home() ? (is_front_page() ? 0 : get_option('page_for_posts')) : null);

		$share_counts = novashare_get_post_share_counts($post_id);
		$share_count = 0;

		//check for minimum share count
		if(empty($share_counts) || (isset($novashare['minimum_share_count']) && (array_sum($share_counts) < $novashare['minimum_share_count']))) {
			unset($share_counts);
		}
	}

	$output = "";

	if(!empty($novashare[$location]['social_networks'])) {

		//remove subscribe button if link isn't set
		if(($key = array_search('subscribe', $novashare[$location]['social_networks'])) !== false && empty($novashare['subscribe_link'])) {
		    unset($novashare[$location]['social_networks'][$key]);
		}

		$networks = novashare_networks();

		//remove mobile networks on desktop
		$mobile_networks = apply_filters('novashare_mobile_networks', array());
		if(!empty($mobile_networks) && is_array($mobile_networks) && !wp_is_mobile()) {
			$novashare[$location]['social_networks'] = array_diff($novashare[$location]['social_networks'], $mobile_networks);
		}

		$shape_class = !empty($novashare[$location]['shape']) ? " ns-" . $novashare[$location]['shape'] : '';

		//build classes
		$container_class = "";
		$wrapper_class = "";
		$button_class = "";
		$icon_class = "";
		$label_class = "";

		//style classes
		if(!empty($novashare[$location]['style'])) {
			if($novashare[$location]['style'] == 'inverse') {
				$button_class = " ns-inverse";
				$icon_class.= " ns-inverse ns-border" . $shape_class;
				$label_class.= " ns-inverse";
			}
			elseif($novashare[$location]['style'] == 'solid-inverse-border') {
				$button_class = " ns-inverse" . $shape_class;
				$label_class.= " ns-border ns-inverse";
			}
			elseif($novashare[$location]['style'] == 'full-inverse-border') {
				$button_class = " ns-inverse ns-border" . $shape_class;
				$icon_class.= " ns-inverse";
				$label_class.= " ns-inverse";
			}
			elseif($novashare[$location]['style'] == 'solid-inverse') {
				$button_class = " ns-inverse";
				$icon_class.= $shape_class;
				$label_class.= " ns-inverse";
			}
			elseif($novashare[$location]['style'] == 'full-inverse') {
				$button_class = " ns-inverse" . $shape_class;
				$icon_class.= " ns-inverse";
				$label_class.= " ns-inverse";
			}
		}
		else {
			$button_class = $shape_class;
		}

		//layout container classes
		if(!empty($novashare[$location]['layout'])) {
			$container_class.= " ns-columns ns-" . $novashare[$location]['layout'];
		}

		//size container class
		if(!empty($novashare[$location]['size'])) {
			$container_class.= " " . $novashare[$location]['size'];
		}

		//share count container classes
		if(!empty($share_counts)) {
			$container_class.= " ns-share-count";
			if(!empty($novashare[$location]['total_share_count'])) {
				$container_class.= " ns-has-total-share-count-";
				$container_class.= (!empty($novashare[$location]['total_share_count_position']) ? $novashare[$location]['total_share_count_position'] : 'after');
			}
		}

		//inverse hover
		if(!empty($novashare[$location]['inverse_hover'])) {
			$container_class.= " ns-inverse-hover";
		}

		//wrapper class
		if(!empty($params['wrapper_class'])) {
			$wrapper_class = ' ' . $params['wrapper_class'];
		}
		
		//add override styles
		global $novashare_inline_css;

		//create array if it doesn't exist yet
		if(!is_array($novashare_inline_css)) {
			$novashare_inline_css = array();
		}

		//make sure we haven't printed css for this location yet
		if(!in_array($location, $novashare_inline_css)) {

			//add used location to global flag
			$novashare_inline_css[] = $location;
			$output.= novashare_print_button_inline_styles($location, $novashare[$location]);
		}

		//buttons container
		$output.= "<div class='ns-buttons ns-" . $location . $container_class . " ns-no-print'>";

			//call to action
			if(!empty($novashare[$location]['cta_text'])) {
				$cta_style = "";
				$cta_style.= !empty($novashare[$location]['cta_font_size']) ? "font-size:" . intval($novashare[$location]['cta_font_size']) . "px;" : "";
				$cta_style.= !empty($novashare[$location]['cta_text_color']) ? "color:" . $novashare[$location]['cta_text_color'] . ";" : "";
				$cta_style.= !empty($novashare[$location]['alignment']) ? "text-align:" . $novashare[$location]['alignment'] . ";" : "";
				$output.= "<div class='ns-inline-cta'" . (!empty($cta_style) ? " style='" . $cta_style . "'" : "") . ">" . apply_filters('novashare_inline_cta', $novashare[$location]['cta_text']) . "</div>";
			}

			$output.= "<div class='ns-buttons-wrapper" . $wrapper_class . "'>";

				$output_buttons = "";

				$novashare[$location]['social_networks'] = apply_filters('novashare_button_networks', $novashare[$location]['social_networks'], $location);

				//remove share button if were in a feed
				if(!is_singular() && in_the_loop()) {
					if(($key = array_search('share', $novashare[$location]['social_networks'])) !== false) {
					    unset($novashare[$location]['social_networks'][$key]);
					}
				}

				//setup copy button url
				$copy_url = ($post && (is_singular() || in_the_loop())) ? '"' . get_the_permalink($post->ID) . '"' : 'window.location.href';

				//print buttons
				foreach($novashare[$location]['social_networks'] as $key => $network) {

					$class = $network;
					if(!empty($share_counts[$network]) && !empty($novashare[$location]['network_share_counts'])) {
						$class.= " ns-share-count";
					}
					$target = '_blank';
					if(in_array($network, array('email', 'sms'))) {
						$target = '_self';
					}

					if($network == 'share') {
						add_action('wp_footer', 'novashare_share_window', 100);
					}

					//button link
					$output_buttons.= "<a href='" . novashare_network_link($network, $location) . "' aria-label='" . novashare_get_action_label($network) . "' target='" . $target . "' class='ns-button " . $class . "' rel='nofollow'";
						if($network == 'pinterest') {
							$output_buttons.= " data-pin-do='none'";
							if(!empty($novashare['pinterest']['share_button_behavior'])) {
								$output_buttons.= " data-pinterest-gallery='1'";
								add_action('novashare_inline_js_queue', 'novashare_pinterest_gallery_js');
							}
						}
						elseif($network == 'copy') {
							$output_buttons.= " onClick='event.preventDefault();navigator.clipboard.writeText(" . $copy_url . ").then(() =&gt; alert(\"" . __('URL Copied', 'novashare') . "\"));'";
						}
						elseif($network == 'mastodon') {
							$output_buttons.= " onClick=\"event.preventDefault();nsMastodonWindow(this);\"";
							add_action('wp_footer', 'novashare_mastodon_window', 100);
						}
						elseif($network == 'print') {
							$output_buttons.= " onClick='event.preventDefault();window.print();'";
						}
						elseif($network == 'share') {
							$output_buttons.= " onClick='event.preventDefault();'";
						}
						$button_attributes = apply_filters('novashare_button_attributes', array(), $network, $location);
						if(!empty($button_attributes) && is_array($button_attributes)) {
							foreach($button_attributes as $att => $val) {
								$output_buttons.= " " . $att . "='" . $val . "'";
							}
						}
					$output_buttons.= ">";

						//button wrapper
						$output_buttons.= "<span class='ns-button-wrapper ns-button-block" . $button_class . "'>";

							//buton icon
							$output_buttons.= "<span class='ns-button-icon ns-button-block" . $icon_class . "'>";
								$output_buttons.= $networks[$network]['icon'];
								if(!empty($share_counts[$network])) {
									$output_buttons.= "<span class='ns-button-share-count'>" . novashare_round_number($share_counts[$network]) . "</span>";
									$share_count = $share_count + $share_counts[$network];
								}
							$output_buttons.= "</span>";

							//button label
							if($location != 'floating') {
								$output_buttons.= "<span class='ns-button-label ns-button-block" . $label_class . (empty($novashare[$location]['labels']) ? " ns-hide" : "") . "'>";
									$output_buttons.= "<span class='ns-button-label-wrapper'>";
										$output_buttons.= $networks[$network]['name'];
									$output_buttons.= "</span>";
								$output_buttons.= "</span>";
							}

						$output_buttons.= "</span>";

					$output_buttons.= "</a>";
				}

				//display overall totalif share button is enabled
				if(!empty($share_counts) && (in_array('share', $novashare[$location]['social_networks']) || $location == 'share')) {
					$share_count = array_sum($share_counts);
				}

				//show total share count
				if(!empty($novashare[$location]['total_share_count']) && !empty($share_count)) {

					$output_total_share_count = novashare_print_total_share_count($share_count);

					if(!empty($novashare[$location]['total_share_count_position'])) {
						$output.= $output_total_share_count . $output_buttons;
					}
					else {
						$output.= $output_buttons . $output_total_share_count;
					}
				}
				else {
					$output.= $output_buttons;
				}

			$output.= "</div>";
		$output.= "</div>";
	}

	//inverse hover js
	if(!empty($novashare[$location]['inverse_hover'])) {
		add_action('wp_footer', 'novashare_inverse_hover_js', 100);
	}

	return $output;
}

function novashare_print_inline_styles($location, $args) {

	$button_margin = !empty($args['button_margin']) || (isset($args['button_margin']) && $args['button_margin'] == 0) ? intval($args['button_margin']) : 10;

	$styles = "";
	if(!empty($args['hide_above_breakpoint'])) {
		$styles.= "@media (min-width: " . ((int)(!empty($args['breakpoint']) ? $args['breakpoint'] : "1200") + 1) . "px) {
			.ns-" . $location . " {
				display: none;
			}
		}";
	}
	$styles.= "@media (max-width: " . (!empty($args['breakpoint']) ? $args['breakpoint'] : "1200px") . ") {
		" . (!empty($args['hide_below_breakpoint']) ? ".ns-" . $location . " { display: none; }" : "") . "
		" . (!empty($args['labels']) && !empty($args['hide_labels_mobile']) ? ".ns-buttons.ns-" . $location . " .ns-button-icon { width: 100%; } .ns-buttons.ns-" . $location . " .ns-button-label { display: none; }" : "") . "
	}";
	if(!empty($args['alignment']) && empty($args['layout'])) {
		$styles.= ".ns-" . $location . ":not(.ns-columns) .ns-buttons-wrapper {
			justify-content: " . ($args['alignment'] == 'right' ? "flex-end" : "center") . ";
		}";
		if($args['alignment'] == 'right') {
			$styles.= "body .ns-" . $location . ":not(.ns-columns) a.ns-button, body .ns-" . $location . " .ns-total-share-count {
				margin: 0px 0px " . $button_margin . "px " . $button_margin . "px;
			}";
		}
		elseif($args['alignment'] == 'center') {
			$styles.= "body .ns-" . $location . ":not(.ns-columns) a.ns-button, body .ns-" . $location . " .ns-total-share-count {
				margin: 0px " . ($button_margin/2) . "px " . $button_margin . "px " . ($button_margin/2) . "px;
			}";
		}
	}
	else {
		//if($button_margin != 10) {
			$styles.= "body .ns-" . $location . " a.ns-button, body .ns-" . $location . " .ns-total-share-count {
				margin: 0px " . $button_margin . "px " . $button_margin . "px 0px;
			}";
		//}
	}
	if(!empty($args['layout']) && $button_margin != 10) {
		$columns = preg_replace('/[^0-9]/', '', $args['layout']);
		$styles.= "body .ns-" . $location . " a.ns-button {
			flex-basis: calc(" . round(100/$columns, 6) . "% - " . (($columns-1)*$button_margin)/$columns . "px);
		}";

	}
	if(!empty($styles)) {
		return "<style>" . $styles . "</style>";
	}
}

//print inline styles for buttons
function novashare_print_button_inline_styles($location, $args) {
	$styles = "";
	if(empty($args['labels'])) {
		$styles.= "body .ns-buttons.ns-" . $location . " .ns-button-icon { width: 100%; }";
	}
	if(!empty($args['button_color'])) {
		$styles.= ".ns-" . $location . " .ns-button { --ns-button-color: " . $args['button_color'] . "; }";
	}
	if(!empty($args['button_hover_color'])) {			
		$styles.= ".ns-" . $location . " .ns-button:hover { --ns-button-color: " . $args['button_hover_color'] . "; }";
		$styles.= "body .ns-" . $location . " a.ns-button:hover .ns-button-wrapper>span { box-shadow: none !important; filter: brightness(1) !important; }"; //remove dynamic darkening
	}
	if(empty($args['inverse_hover'])) {
		if(!empty($args['icon_color'])) {
			$styles.= ".ns-" . $location . " .ns-button-icon *, .ns-" . $location . " .ns-button-label span { color: " . $args['icon_color'] . "; }";
		}
		if(!empty($args['icon_hover_color'])) {			
			$styles.= ".ns-" . $location . " .ns-button:hover .ns-button-icon *, .ns-" . $location . " .ns-button:hover .ns-button-label span { color: " . $args['icon_hover_color'] . "; }";
		}
	}
	if(!empty($args['total_share_count'])) {
		if(!empty($args['total_share_count_color'])) {
			$styles.= "body .ns-" . $location . " .ns-total-share-count { color: " . $args['total_share_count_color'] . "; }";
		}
	}
	if(!empty($styles)) {
		return "<style>" . $styles . "</style>";
	}
}

//print total share count HTML
function novashare_print_total_share_count($share_count) {

	if(empty($share_count)) {
		return;
	}

	$output = '<div class="ns-total-share-count">';
		$output.= '<div class="ns-total-share-count-wrapper">';
			$output.= '<div class="ns-total-share-count-details">';
					$output.= '<div class="ns-total-share-count-amount">' . novashare_round_number($share_count) . '</div>';
					$output.= '<div class="ns-total-share-count-text">' . ($share_count > 1 ? apply_filters('novashare_total_share_count_text_plural', __('SHARES', 'novashare')) : apply_filters('novashare_total_share_count_text_singular', __('SHARE', 'novashare'))) . '</div>';
			$output.= '</div>';
		$output.= '</div>';
	$output.= '</div>';

	return $output;
}

function novashare_share_window() {

	$novashare = get_option('novashare');

	//share window styles
	echo '<style>';
		echo '#ns-share-window-wrapper{opacity:0;display:none;justify-content:center;align-items:center;position:fixed;top:0;bottom:0;left:0;right:0;z-index:99999;background:rgba(0,0,0,.8);transition:opacity .3s}#ns-share-window-wrapper.ns-visible-window{display:flex}#ns-share-window-wrapper.ns-animate-window{opacity:1}#ns-share-window{position:relative;width:720px;max-height:90%;max-width:90%;border-radius:5px;overflow:hidden}#ns-share-window-cta{display:flex;justify-content:space-between;align-items:center;background:' . (!empty($novashare['share']['cta_background_color']) ? $novashare['share']['cta_background_color'] : '#4d4595') . ';padding:10px;color:' . (!empty($novashare['share']['font_color']) ? $novashare['share']['font_color'] : '#fff') . ';font-size: 18px;}#ns-share-window-close{display:flex;cursor:pointer;opacity:.9}#ns-share-window-close:hover{opacity:1}#ns-share-window-cta svg{height:18px;width:18px;pointer-events:none}#ns-share-window-content{background:#fff;padding:15px 15px 5px 15px;max-height:70vh;overflow-y:auto}@media (max-width:800px){#ns-share-window .ns-button{flex-basis:calc(50% - 5px);margin-right:10px}#ns-share-window .ns-button:nth-of-type(2n){margin-right:0}@media (max-width:640px){#ns-share-window .ns-button{flex-basis:100%;margin:0 0 10px}}}';
	
		//total share count
		if(!empty($novashare['share']['total_share_count'])) {
			echo'#ns-share-window .ns-buttons{position:static}#ns-share-window .ns-buttons-wrapper{margin:0}#ns-share-window .ns-total-share-count{color:' . (!empty($novashare['share']['font_color']) ? $novashare['share']['font_color'] : '#fff') . ';top:0;right:45px;height:47px;width:auto;font-size:11px}#ns-share-window .ns-total-share-count-details{display:flex;align-items:center}#ns-share-window .ns-total-share-count-text{line-height:normal;font-size:11px;margin-left:3px}';
		}
	echo '</style>';

	//share window
	echo '<div id="ns-share-window-wrapper">';
		echo '<div id="ns-share-window">';
			echo '<div id="ns-share-window-cta">';
				echo '<div>' . (!empty($novashare['share']['cta_text']) ? $novashare['share']['cta_text'] : __('Share to...', 'novashare')) . '</div>';
				echo '<div id="ns-share-window-close"><svg role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="currentColor" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></div>';
			echo '</div>';
			echo '<div id="ns-share-window-content">';
				echo novashare_print_buttons('share');
			echo '</div>';
		echo '</div>';
	echo '</div>';

	//share window script
	echo '<script>var shareButtons=document.querySelectorAll(".ns-button.share"),shareWrapper=document.getElementById("ns-share-window-wrapper");shareButtons.forEach(function(e){e.addEventListener("click",function(e){shareWrapper.classList.add("ns-visible-window"),setTimeout(function(){shareWrapper.classList.add("ns-animate-window")},10)})}),shareWrapper.addEventListener("click",function(e){e.target!==this&&"ns-share-window-close"!==e.target.getAttribute("id")||shareWrapper.classList.remove("ns-visible-window","ns-animate-window")});</script>';
}

function novashare_mastodon_window() {

	//mastodon window styles
	echo '<style>#ns-mastodon-window-wrapper{opacity:0;display:none;justify-content:center;align-items:center;position:fixed;top:0;bottom:0;left:0;right:0;z-index:99999;background:rgba(0,0,0,.8);transition:opacity .3s}#ns-mastodon-window-wrapper.ns-visible-window{display:flex}#ns-mastodon-window-close:hover,#ns-mastodon-window-wrapper.ns-animate-window{opacity:1}#ns-mastodon-window{width:720px;max-height:90%;max-width:90%;border-radius:3px;overflow:hidden}#ns-mastodon-window-cta{display:flex;justify-content:space-between;align-items:center;background:#6364ff;padding:10px;color:#fff;font-size:18px}#ns-mastodon-window-close{display:flex;cursor:pointer;opacity:.9}#ns-mastodon-window-cta svg{height:18px;width:18px;pointer-events:none}#ns-mastodon-window-content{display:flex;background:#fff;padding:15px;max-height:70vh;overflow-y:auto}#ns-mastodon-instance{background:#fafafa;color:#666;border:1px solid #ddd;padding:10px 15px;box-sizing:border-box;max-width:100%}#ns-mastodon-instance-submit{border:none;background:#555;cursor:pointer;padding:10px 20px;color:#fff;text-decoration:none;text-transform:none}#ns-mastodon-instance-submit:hover{background:#444}#ns-mastodon-instance,#ns-mastodon-instance-submit{border-radius:3px;font-size:16px;font-weight:400}</style>';

	//mastodon window
	echo '<div id="ns-mastodon-window-wrapper">';
		echo '<div id="ns-mastodon-window">';
			echo '<div id="ns-mastodon-window-cta">';
				echo '<div>' . __('Your Mastodon Instance', 'novashare') . '</div>';
				echo '<div id="ns-mastodon-window-close"><svg role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="currentColor" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></div>';
			echo '</div>';
			echo '<div id="ns-mastodon-window-content">';
				echo '<input type="text" id="ns-mastodon-instance" placeholder="https://mastodon.social" style="flex-grow: 1;">';
				echo '<input type="button" id="ns-mastodon-instance-submit" value="Submit" style="margin-left: 10px;" onclick="nsMastodonShare();" />';
			echo '</div>';
		echo '</div>';
	echo '</div>';

	//mastodon window script
	echo '<script>var nsMastodonURL,nsMastodonWrapper=document.getElementById("ns-mastodon-window-wrapper");function nsMastodonWindow(n){nsMastodonURL=n.href;var t=document.getElementById("ns-mastodon-window-wrapper");t.classList.add("ns-visible-window"),setTimeout(function(){t.classList.add("ns-animate-window")},10)}function nsMastodonShare(){var n=nsMastodonURL,t=document.getElementById("ns-mastodon-instance").value;if(t){var s=t.replace(/\/$/,"");s=-1===s.indexOf("://")?"https://"+s:s,n=nsMastodonURL.replace("https://mastodon.social",s)}window.open(n,"_blank","toolbar=0,status=0,menubar=0,scrollbars=0,width=800,height=575,top=200,left="+(window.innerWidth-700)/2)}nsMastodonWrapper.addEventListener("click",function(n){n.target!==this&&"ns-mastodon-window-close"!==n.target.getAttribute("id")&&"ns-mastodon-instance-submit"!==n.target.getAttribute("id")||nsMastodonWrapper.classList.remove("ns-visible-window","ns-animate-window")});</script>';
}

function novashare_get_action_label($network) {

	$networks = novashare_networks();

	switch($network) {
		case 'copy' :
			return __('Copy share link', 'novashare');
		case 'email' :
		case 'sms' :
			return __('Share via', 'novashare') . ' ' . $networks[$network]['name'];
			break;
		case 'print' :
			return __('Print this page', 'novashare');
			break;
		case 'share' :
			return __('Share on more networks', 'novashare');
			break;
		default :
			return __('Share on', 'novashare') . ' ' . $networks[$network]['name'];
			break;
	}
}

//round number to nearest thousand, million, billion, trillion
function novashare_round_number($num) {

    if($num > 1000) {
        $x = round($num);
        $x_number_format = number_format($x);
        $x_array = explode(',', $x_number_format);
        $x_parts = array('k', 'm', 'b', 't');
        $x_count_parts = count($x_array) - 1;
        $x_display = $x;
        $x_display = $x_array[0] . ((int) $x_array[1][0] !== 0 && $x_array[0] < 99 ? '.' . $x_array[1][0] : '');
        $x_display .= $x_parts[$x_count_parts - 1];
        return $x_display;
    }

    return $num;
}

//get shortened bitly link from url
function novashare_bitly_link($url, $network) {

	global $wpdb;
	global $post_short_links;

	$post = novashare_get_global_post();

	$post_id = is_singular() ? $post->ID : (is_home() ? (is_front_page() ? 0 : get_option('page_for_posts')) : null);

	if(!isset($post_id)) {
		return $url;
	}

	$novashare = get_option('novashare');

	if(empty($post_short_links)) {

		//get existing short links row
		$short_links_row = $wpdb->get_row($wpdb->prepare("SELECT id, meta_value FROM {$wpdb->prefix}novashare_meta WHERE post_id = %d AND meta_key = 'short_links'", $post_id));

		//unpack short links array
		$post_short_links = !empty($short_links_row->meta_value) ? maybe_unserialize($short_links_row->meta_value) : array();
	}

	//check if the short link we need exists already
	if(!empty($post_short_links)) {
		if(!empty($novashare['google_utm']) && !empty($post_short_links[$network])) {
			return $post_short_links[$network]; //network specific
		}
		elseif(!empty($short_links['url'])) {
			return $post_short_links['url']; //base url
		}
	}

	//filter short link
	$short_link = apply_filters('novashare_short_link_url', '', $url, $network);

	//check for bitly access token
	if(empty($short_link) && !empty($novashare['bitly_access_token'])) {

		//request short link from bitly
		$bitly_request_args = array( 
			'timeout' => 15,
			'body' => json_encode(array(
				'long_url' => $url,
				'group_guid' =>  ($novashare['bitly_group_guid'] ? $novashare['bitly_group_guid'] : '')
			)),
			'headers' => array(
				'Authorization' => 'Bearer ' . $novashare['bitly_access_token'],
				'Content-type' => 'application/json'
			)
		);

		$bitly_response = wp_remote_post('https://api-ssl.bitly.com/v4/shorten', $bitly_request_args);
		
		//successful response
		if(in_array(wp_remote_retrieve_response_code($bitly_response), array(200, 201)) && wp_remote_retrieve_body($bitly_response) != '') {
			$body = json_decode(wp_remote_retrieve_body($bitly_response), ARRAY_A);

			//check if new short link is present
			if(!empty($body['link'])) {
				$short_link = $body['link'];
			}
		}
	}

	//store shortened url
	if(!empty($short_link)) {

		//update post meta short links array
		$post_short_links[!empty($novashare['google_utm']) ? $network : 'url'] = $short_link;

		//check if there are multiple existing short link rows
		$existing_row_count = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM {$wpdb->prefix}novashare_meta WHERE post_id = %d AND meta_key = 'short_links'", $post_id));
		if($existing_row_count > 1) {

			//delete existing short link rows for post except selected row
			$wpdb->query($wpdb->prepare("DELETE FROM {$wpdb->prefix}novashare_meta WHERE meta_key = %s AND post_id = %d AND id <> %d", 'short_links', $post_id, (!empty($short_links_row->id) ? $short_links_row->id : '')));
		}

		//update/insert short links row
		$wpdb->replace($wpdb->prefix . 'novashare_meta', array(
				'id'         => (!empty($short_links_row->id) ? $short_links_row->id : ''),
				'post_id'    => $post_id,
				'meta_key'   => 'short_links',
				'meta_value' => maybe_serialize($post_short_links)
			),
			array(
				'%d',
				'%d',
				'%s',
				'%s'
			)
		);

		do_action('novashare_bitly_link_generated', $short_link, $post_id, (!empty($novashare['google_utm']) ? $network : null));

		return $short_link;
	}

	return $url;
}

//get global post object
function novashare_get_global_post() {
	$post = get_queried_object();

	if(!$post instanceof \WP_Post) {
		return false;
	}

	return $post;
}

//run before main novashare option update is processed and adjust saved values
function novashare_pre_update_option_novashare($new_value, $old_value) {

	//handle bitly groups
	if(!empty($new_value['bitly_access_token'])) {

		if($new_value['bitly_access_token'] == $old_value['bitly_access_token'] && !empty($old_value['bitly_groups'])) {
			$new_value['bitly_groups'] = $old_value['bitly_groups'];
		}
		else {

		    $bitly_request_args = array( 
		        'timeout' => 15,
		        'headers' => array(
		        	'Authorization' => 'Bearer ' . $new_value['bitly_access_token']
		        )
		    );
		    $response = wp_remote_get('https://api-ssl.bitly.com/v4/groups', $bitly_request_args);

		    if(wp_remote_retrieve_response_code($response) == 200 && wp_remote_retrieve_body($response ) != '') {
		        
		        //valid token response
		        $body = json_decode(wp_remote_retrieve_body($response), ARRAY_A);;

		        //organize response groups
		        if(!empty($body['groups'])) {
		            $groups = array();
		            foreach($body['groups'] as $group) {
		                $groups[$group['guid']] = $group['name'];
		            }

		            //save the groups to options array
		            $new_value['bitly_groups'] = $groups;

		            //save the guid value for the default group
		            $new_value['bitly_group_guid'] = $body['groups'][0]['guid'];
		        }
		    }
		    elseif (wp_remote_retrieve_response_code($response) == 403) {
		        //invalid token response
		    }
		}
	}

	//generate facebook app access token
	if(!empty($new_value['facebook_app_id']) && !empty($new_value['facebook_app_secret'])) {

		if(($new_value['facebook_app_id'] !== $old_value['facebook_app_id']) || ($new_value['facebook_app_secret'] !== $old_value['facebook_app_secret']) || empty($old_value['facebook_app_access_token'])) {

			$response = wp_remote_post(add_query_arg(
				array(
					'client_id' => trim($new_value['facebook_app_id']), 
					'client_secret' => trim($new_value['facebook_app_secret']), 
					'grant_type' => 'client_credentials'
				), 
				'https://graph.facebook.com/oauth/access_token' 
			));

			if(!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {

				$body = wp_remote_retrieve_body($response);
				$body = json_decode($body, true);

				if(!empty($body['access_token']) && strpos($body['access_token'], '|') !== false) {
					$new_value['facebook_app_access_token'] = $body['access_token'];
				}
			}
		}
		//preserve previous token if nothing changed
		elseif(!empty($old_value['facebook_app_access_token'])) {
			$new_value['facebook_app_access_token'] = $old_value['facebook_app_access_token'];
		}
	}

	//proceed with option update
	return $new_value;
}

//add filter to update options
function novashare_update_options() {

	//novashare
	add_filter('pre_update_option_novashare', 'novashare_pre_update_option_novashare', 10, 2);
}
add_action('admin_init', 'novashare_update_options');

//amp endpoint check
function novashare_is_amp() {
	return function_exists('is_amp_endpoint') && is_amp_endpoint();
}

//admin init action
function novashare_admin_init() {
	add_action('delete_post', 'novashare_delete_post');
}
add_action('admin_init', 'novashare_admin_init');

//remove novashare post data on deletion
function novashare_delete_post($pid) {
	global $wpdb;
    $wpdb->query($wpdb->prepare("DELETE FROM {$wpdb->prefix}novashare_meta WHERE post_id = %d", $pid));
}

//add compatability filter for used css
add_action('init', function() {
	if(defined('PERFMATTERS_VERSION')) {
		add_filter('perfmatters_rucss_excluded_selectors', function($selectors) {
			$selectors[] = '.ns-button.ns-share-count';
			$selectors[] = '.ns-button-share-count';
			$selectors[] = '.ns-total-share-count';
			$selectors[] = '.ns-total-share-count-wrapper';
			$selectors[] = '.ns-total-share-count-amount';
			$selectors[] = '.ns-total-share-count-text';
		  	return $selectors;
		});
	}
});

/* EDD License Functions
/***********************************************************************/

//activate license
function novashare_activate_license() {

	//grab existing license data
	$novashare_license = is_network_admin() ? get_site_option('novashare_license') : get_option('novashare_license');
	$license = !empty($novashare_license['key']) ? trim($novashare_license['key']) : null;

	if(!empty($license)) {

		//data to send in our API request
		$api_params = array(
			'edd_action'=> 'activate_license',
			'license' 	=> $license,
			'item_name' => urlencode(NOVASHARE_ITEM_NAME),
			'url'       => home_url()
		);

		//call the custom API
		$response = wp_remote_post(NOVASHARE_STORE_URL, array('timeout' => 15, 'sslverify' => true, 'body' => $api_params));

		//make sure the response came back okay
		if(is_wp_error($response)) {
			return false;
		}

		//decode the license data
		$license_data = json_decode(wp_remote_retrieve_body($response));

		//store status
		$novashare_license['status'] = $license_data->license;

		//update stored option
		if(is_network_admin()) {
			update_site_option('novashare_license', $novashare_license);
		}
		else {
			update_option('novashare_license', $novashare_license);
		}
	}
}

//deactivate license
function novashare_deactivate_license() {

	//grab existing license data
	$novashare_license = is_network_admin() ? get_site_option('novashare_license') : get_option('novashare_license');
	$license = !empty($novashare_license['key']) ? trim($novashare_license['key']) : null;

	if(!empty($license)) {

		//data to send in our API request
		$api_params = array(
			'edd_action'=> 'deactivate_license',
			'license' 	=> $license,
			'item_name' => urlencode(NOVASHARE_ITEM_NAME),
			'url'       => home_url()
		);

		//call the custom API
		$response = wp_remote_post(NOVASHARE_STORE_URL, array('timeout' => 15, 'sslverify' => true, 'body' => $api_params));

		//make sure the response came back okay
		if(is_wp_error($response)) {
			return false;
		}

		//decode the license data
		$license_data = json_decode(wp_remote_retrieve_body($response));

		//$license_data->license will be either "deactivated" or "failed"
		if($license_data->license == 'deactivated') {

			//remove stored status
			unset($novashare_license['status']);

			//update license option
			if(is_network_admin()) {
				update_site_option('novashare_license', $novashare_license);
			}
			else {
				update_option('novashare_license', $novashare_license);
			}	
		}
	}
}

//check + retrieve license status
function novashare_check_license() {

	//grab existing license data
	$novashare_license = is_network_admin() ? get_site_option('novashare_license') : get_option('novashare_license');
	$license = !empty($novashare_license['key']) ? trim($novashare_license['key']) : null;

	if(!empty($license)) {

		//data to send in our API request
		$api_params = array(
			'edd_action' => 'check_license',
			'license' => $license,
			'item_name' => urlencode(NOVASHARE_ITEM_NAME),
			'url'       => home_url()
		);

		//call the custom API
		$response = wp_remote_post(NOVASHARE_STORE_URL, array('timeout' => 15, 'sslverify' => true, 'body' => $api_params));

		//make sure the response came back okay
		if(is_wp_error($response)) {
			return false;
		}

		//decode the license data
		$license_data = json_decode(wp_remote_retrieve_body($response));

		//save stored status
		$novashare_license['status'] = $license_data->license;

		//update license option
		if(is_network_admin()) {
			update_site_option('novashare_license', $novashare_license);
		}
		else {
			update_option('novashare_license', $novashare_license);
		}
		
		//return license data for use
		return($license_data);
	}

	return false;
}