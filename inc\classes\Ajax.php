<?php
namespace Novashare;

class Ajax
{

	public function __construct() 
	{
		add_action('wp_ajax_novashare_save_settings', array('Novashare\Ajax', 'save_settings'));
		add_action('wp_ajax_novashare_restore_defaults', array('Novashare\Ajax', 'restore_defaults'));
		add_action('wp_ajax_novashare_export_settings', array('Novashare\Ajax', 'export_settings'));
		add_action('wp_ajax_novashare_import_settings', array('Novashare\Ajax', 'import_settings'));
		add_action('wp_ajax_novashare_purge_share_counts', array('Novashare\Ajax', 'purge_share_counts'));
		add_action('wp_ajax_novashare_purge_short_links', array('Novashare\Ajax', 'purge_short_links'));
		add_action('wp_ajax_novashare_close_cta', array('Novashare\Ajax', 'close_cta'));
	}

	//save settings ajax action
	public static function save_settings() {

		self::security_check();

		parse_str(stripslashes($_POST['form']), $form);
		
		if(!empty($form['novashare'])) {
			update_option('novashare', $form['novashare']);
		}
		
		if(!empty($form['novashare_tools'])) {
			update_option('novashare_tools', $form['novashare_tools']);
		}

		wp_send_json_success(array(
		    'message' => __('Settings saved.', 'novashare'), 
		));
	}

	//restore defaults ajax action
	public static function restore_defaults() {

		self::security_check();

		$defaults = novashare_default_options();
		
		if(!empty($defaults)) {
			update_option("novashare", $defaults);
		}

		wp_send_json_success(array(
	    	'message' => __('Successfully restored default options.', 'novashare'),
	    	'reload' => true
		));
	}

	//export settings ajax settings
	public static function export_settings() {

		self::security_check();

		$settings = array();

		$settings['novashare'] = get_option('novashare');
		$settings['novashare_tools'] = get_option('novashare_tools');

		wp_send_json_success(array(
		    'message' => __('Settings exported.', 'novashare'), 
		    'export' => json_encode($settings)
		));
	}

	//import settings ajax action
	public static function import_settings() {

		self::security_check();

		if(!empty($_FILES)) {
			$import_file = $_FILES['novashare_import_settings_file']['tmp_name'];
		}

		//cancel if there's no file
		if(empty($import_file)) {
			wp_send_json_error(array(
		    	'message' => __('No import file given.', 'novashare')
			));
		}

		//check if uploaded file is valid
		$file_parts = explode('.', $_FILES['novashare_import_settings_file']['name']);
		$extension = end($file_parts);
		if($extension != 'json') {
			wp_send_json_error(array(
		    	'message' => __('Please upload a valid .json file.', 'novashare')
			));
		}

		//unpack settings from file
		$settings = (array) json_decode(file_get_contents($import_file), true);

		if(isset($settings['novashare'])) {
			update_option('novashare', $settings['novashare']);
		}

		if(isset($settings['novashare_tools'])) {
			update_option('novashare_tools', $settings['novashare_tools']);
		}

		wp_send_json_success(array(
	    	'message' => __('Successfully imported Novashare settings.', 'novashare'),
	    	'reload' => true
		));

	}

	//purge share counts ajax action
	public static function purge_share_counts() {

		self::security_check();

		global $wpdb;

		$novashare_table = $wpdb->prefix . 'novashare_meta';

		//delete all share counts
		$result = $wpdb->query($wpdb->prepare("DELETE FROM $novashare_table WHERE meta_key = %s OR meta_key = %s OR meta_key = %s OR meta_key = %s", 'share_counts', 'share_counts_updated', 'recovery_share_counts', 'recovery_urls'));

		//display result message
		if($result !== false) {
			wp_send_json_success(array(
		    	'message' => __('Share counts purged.', 'novashare')
			));
		} 
		else {
			wp_send_json_error(array(
		    	'message' => __('Share counts not purged.', 'novashare')
			));
		}
	}

	//purge short links ajax action
	public static function purge_short_links() {

		self::security_check();

		global $wpdb;

		//delete all short links
		$result = $wpdb->delete($wpdb->prefix . 'novashare_meta', array('meta_key' => 'short_links'));

		//display result message
		if($result !== false) {
			wp_send_json_success(array(
		    	'message' => __('Short links purged.', 'novashare')
			));

		}
		else {
			wp_send_json_error(array(
		    	'message' => __('Short links not purged.', 'novashare')
			));
		}
	}

	//close cta ajax action
	public static function close_cta() {
		self::security_check();
		if(update_option('novashare_close_cta', 1)) {
			wp_send_json_success();
		}
	}

	//ajax security check
	public static function security_check() {

		if(!current_user_can('manage_options')) {

			wp_send_json_error(array(
		    	'message' => __('Permission denied.', 'novashare')
			));
		}

		if(!check_ajax_referer('novashare-nonce', 'nonce', false)) {

		    wp_send_json_error(array(
		    	'message' => __('Nonce is invalid.', 'novashare')
			));
		}
	}


}