# Custom Social Share Plugin

A lightweight WordPress plugin for social sharing with Facebook, X (Twitter), LinkedIn, and Copy Link buttons that automatically uses short URLs (`/?p=123` format).

## Features

- ✅ **4 Social Networks**: Facebook, X (Twitter), LinkedIn, Copy Link
- ✅ **Short URLs**: Automatically uses `yoursite.com/?p=123` format
- ✅ **Lightweight**: No external dependencies
- ✅ **Mobile Responsive**: Works perfectly on all devices
- ✅ **3 Style Options**: Default, Minimal, Rounded
- ✅ **Accessible**: Full keyboard navigation and screen reader support
- ✅ **Copy Functionality**: Advanced clipboard API with fallbacks
- ✅ **Analytics Ready**: Built-in tracking for Google Analytics & Facebook Pixel

## Installation

1. **Upload the plugin folder** to `/wp-content/plugins/custom-social-share/`
2. **Activate the plugin** through the WordPress admin
3. **Use the shortcode** `[custom_share_buttons]` in your posts/pages

## Usage

### Basic Shortcode

```php
[custom_share_buttons]
```

### Advanced Options

```php
[custom_share_buttons 
    title="Custom Share Title" 
    url="https://example.com/?p=123"
    show_labels="true" 
    style="default"]
```

### Parameters

- **`title`** - Custom title for sharing (default: post title)
- **`url`** - Custom URL to share (default: short URL `/?p=123`)
- **`show_labels`** - Show button labels (`true`/`false`, default: `true`)
- **`style`** - Button style (`default`/`minimal`/`rounded`, default: `default`)

### Style Examples

**Default Style (Colored buttons):**
```php
[custom_share_buttons style="default"]
```

**Minimal Style (Outlined buttons):**
```php
[custom_share_buttons style="minimal"]
```

**Rounded Style (Pill-shaped buttons):**
```php
[custom_share_buttons style="rounded"]
```

**Icon Only (No labels):**
```php
[custom_share_buttons show_labels="false"]
```

## Replacing Novashare

To replace your existing `[novashare_inline_content]` shortcode:

1. **Find and replace** in your theme files:
   ```
   [novashare_inline_content] → [custom_share_buttons]
   ```

2. **Or use both** during transition:
   ```php
   [custom_share_buttons]
   <!-- [novashare_inline_content] -->
   ```

## Browser Support

- ✅ **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- ✅ **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- ✅ **Copy Functionality**: Clipboard API + fallback for older browsers
- ✅ **Accessibility**: WCAG 2.1 AA compliant

## Technical Details

### Short URL Generation

The plugin automatically generates short URLs using WordPress's built-in format:
- **Long URL**: `yoursite.com/blog/my-awesome-post/`
- **Short URL**: `yoursite.com/?p=123`

### Copy Button Technology

1. **Primary**: Modern Clipboard API (HTTPS required)
2. **Fallback**: `document.execCommand('copy')` for older browsers
3. **Final Fallback**: Manual copy prompt

## Troubleshooting

### Copy Button Not Working

1. **Check HTTPS**: Clipboard API requires secure context
2. **Check Console**: Look for JavaScript errors
3. **Test Fallback**: Should show manual copy prompt

### Buttons Not Showing

1. **Check Shortcode**: Ensure `[custom_share_buttons]` is correct
2. **Check Post Context**: Must be used within a post/page
3. **Check CSS**: Ensure styles are loading

## License

GPL v2 or later
