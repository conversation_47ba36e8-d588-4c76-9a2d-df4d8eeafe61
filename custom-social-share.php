<?php
/**
 * Plugin Name: Custom Social Share
 * Plugin URI: https://azaramedia.com
 * Description: Lightweight social sharing plugin with Facebook, X (Twitter), LinkedIn, and Copy Link buttons using short URLs.
 * Version: 1.0.0
 * Author: Azara Media
 * Author URI: https://azaramedia.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: custom-social-share
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CUSTOM_SOCIAL_SHARE_VERSION', '1.0.0');
define('CUSTOM_SOCIAL_SHARE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('CUSTOM_SOCIAL_SHARE_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main plugin class
 */
class Custom_Social_Share {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('custom-social-share', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Add shortcode
        add_shortcode('custom_share_buttons', array($this, 'render_share_buttons'));
        
        // Enqueue styles and scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
    }
    
    /**
     * Enqueue CSS and JavaScript
     */
    public function enqueue_assets() {
        wp_enqueue_style(
            'custom-social-share-css',
            CUSTOM_SOCIAL_SHARE_PLUGIN_URL . 'assets/style.css',
            array(),
            CUSTOM_SOCIAL_SHARE_VERSION
        );
        
        wp_enqueue_script(
            'custom-social-share-js',
            CUSTOM_SOCIAL_SHARE_PLUGIN_URL . 'assets/script.js',
            array(),
            CUSTOM_SOCIAL_SHARE_VERSION,
            true
        );
    }
    
    /**
     * Render share buttons shortcode
     */
    public function render_share_buttons($atts) {
        // Parse attributes
        $atts = shortcode_atts(array(
            'title' => '',
            'url' => '',
            'show_labels' => 'true',
            'style' => 'default' // default, minimal, rounded
        ), $atts, 'custom_share_buttons');
        
        // Get current post data
        global $post;
        if (!$post) {
            return '';
        }
        
        // Get share data
        $share_title = !empty($atts['title']) ? $atts['title'] : get_the_title($post->ID);
        $share_url = !empty($atts['url']) ? $atts['url'] : home_url('/?p=' . $post->ID);
        $show_labels = $atts['show_labels'] === 'true';
        $style_class = 'css-style-' . sanitize_html_class($atts['style']);
        
        // Build output
        ob_start();
        ?>
        <div class="custom-social-share <?php echo esc_attr($style_class); ?>">
            <div class="css-buttons-wrapper">
                
                <!-- Facebook -->
                <a href="<?php echo esc_url($this->get_facebook_url($share_url)); ?>" 
                   target="_blank" 
                   rel="noopener noreferrer" 
                   class="css-button css-facebook"
                   aria-label="<?php esc_attr_e('Share on Facebook', 'custom-social-share'); ?>">
                    <span class="css-icon">
                        <?php echo $this->get_facebook_icon(); ?>
                    </span>
                    <?php if ($show_labels): ?>
                        <span class="css-label"><?php esc_html_e('Facebook', 'custom-social-share'); ?></span>
                    <?php endif; ?>
                </a>
                
                <!-- X (Twitter) -->
                <a href="<?php echo esc_url($this->get_twitter_url($share_url, $share_title)); ?>" 
                   target="_blank" 
                   rel="noopener noreferrer" 
                   class="css-button css-twitter"
                   aria-label="<?php esc_attr_e('Share on X (Twitter)', 'custom-social-share'); ?>">
                    <span class="css-icon">
                        <?php echo $this->get_twitter_icon(); ?>
                    </span>
                    <?php if ($show_labels): ?>
                        <span class="css-label"><?php esc_html_e('X (Twitter)', 'custom-social-share'); ?></span>
                    <?php endif; ?>
                </a>
                
                <!-- LinkedIn -->
                <a href="<?php echo esc_url($this->get_linkedin_url($share_url, $share_title)); ?>" 
                   target="_blank" 
                   rel="noopener noreferrer" 
                   class="css-button css-linkedin"
                   aria-label="<?php esc_attr_e('Share on LinkedIn', 'custom-social-share'); ?>">
                    <span class="css-icon">
                        <?php echo $this->get_linkedin_icon(); ?>
                    </span>
                    <?php if ($show_labels): ?>
                        <span class="css-label"><?php esc_html_e('LinkedIn', 'custom-social-share'); ?></span>
                    <?php endif; ?>
                </a>
                
                <!-- Copy Link -->
                <button type="button" 
                        class="css-button css-copy" 
                        data-url="<?php echo esc_attr($share_url); ?>"
                        aria-label="<?php esc_attr_e('Copy link', 'custom-social-share'); ?>">
                    <span class="css-icon">
                        <?php echo $this->get_copy_icon(); ?>
                    </span>
                    <?php if ($show_labels): ?>
                        <span class="css-label"><?php esc_html_e('Copy Link', 'custom-social-share'); ?></span>
                    <?php endif; ?>
                </button>
                
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get Facebook share URL
     */
    private function get_facebook_url($url) {
        return 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($url);
    }
    
    /**
     * Get Twitter share URL
     */
    private function get_twitter_url($url, $title) {
        return 'https://x.com/intent/tweet?text=' . urlencode($title) . '&url=' . urlencode($url);
    }
    
    /**
     * Get LinkedIn share URL
     */
    private function get_linkedin_url($url, $title) {
        return 'https://www.linkedin.com/shareArticle?mini=true&url=' . urlencode($url) . '&title=' . urlencode($title);
    }
    
    /**
     * Get Facebook icon SVG
     */
    private function get_facebook_icon() {
        return '<svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>';
    }
    
    /**
     * Get Twitter icon SVG
     */
    private function get_twitter_icon() {
        return '<svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/></svg>';
    }
    
    /**
     * Get LinkedIn icon SVG
     */
    private function get_linkedin_icon() {
        return '<svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>';
    }
    
    /**
     * Get Copy icon SVG
     */
    private function get_copy_icon() {
        return '<svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            wp_die(__('This plugin requires WordPress version 5.0 or higher.', 'custom-social-share'));
        }
        
        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            wp_die(__('This plugin requires PHP version 7.4 or higher.', 'custom-social-share'));
        }
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
    }
}

// Initialize the plugin
new Custom_Social_Share();
