<?php
add_action('template_redirect', function() {

	//only single posts
	if(!is_singular()) {
		return;
	}

	//not on mobile when possible
	if(wp_is_mobile()) {
		return;
	}

	$novashare = get_option('novashare');

	//option filter
	if(empty(apply_filters('novashare_highlight', !empty($novashare['highlight']['enabled'])))) {
		return;
	}

	//post type selection
	$post = novashare_get_global_post();

	if(!isset($post) || empty($post->ID) || empty($post->post_type)) {
		return;
	}

	if(empty($novashare['highlight']['post_types']) || !in_array($post->post_type, $novashare['highlight']['post_types'])) {
		return;
	}

	//post meta details
	$details = novashare_get_post_details($post->ID);

	if(!empty($details['disable_highlight'])) {
		return;
	}

	//hooks
	add_action('wp_footer', 'novashare_highlight_print');
	add_filter('the_content', 'novashare_content_marker', 999);
	add_action('novashare_inline_js_queue', 'novashare_highlight_js');
});

//print highlight buttons
function novashare_highlight_print() {
	
	$novashare = get_option('novashare');
	
	//inline styles
	echo '<style>#ns-highlight-share{visibility:hidden;opacity:0;position:absolute;bottom:0px;left:0px;z-index:999}#ns-highlight-share.ns-visible{visibility:visible;opacity:1;transition:opacity .2s linear}.ns-highlight a.ns-button{margin:0;overflow:visible;position:relative;background:var(--ns-button-color)}.ns-highlight a.ns-button::before{content:"";box-shadow:0 3px 15px 1px rgb(0 0 0 / .2);z-index:-1;position:absolute;top:0;left:0;right:0;bottom:0}';
	
		//container shape styles
		if(!empty($novashare['highlight']['container_shape'])) {
			$radius = ($novashare['highlight']['container_shape'] == 'rounded' ? '5' : '40') . 'px';
			echo '.ns-highlight a.ns-button:first-of-type,.ns-highlight a.ns-button:first-of-type:before,.ns-highlight a.ns-button:first-of-type .ns-button-wrapper{border-top-left-radius:' . $radius . ';border-bottom-left-radius:' . $radius . '}';
			echo '.ns-highlight a.ns-button:last-of-type,.ns-highlight a.ns-button:last-of-type:before,.ns-highlight a.ns-button:last-of-type .ns-button-wrapper{border-top-right-radius:' . $radius . ';border-bottom-right-radius:' . $radius . '}';
		}
		
	echo'</style>';
	
	//print buttons
	echo '<div id="ns-highlight-share">';
		echo novashare_print_buttons('highlight');
	echo '</div>';
}

//add marker to content area
function novashare_content_marker($content) {

	static $has_run = false;

	if($has_run) {
		return $content;
	}

	global $wp_current_filter;

	//bail if the_content is being requested by something else
    if(!empty($wp_current_filter) && is_array($wp_current_filter)) {
    	if(count(array_intersect($wp_current_filter, apply_filters('novashare_content_marker_excluded_filters', array('wp_head', 'get_the_excerpt', 'widget_text_content', 'p3_content_end')))) > 0) {
	     	return $content;
		}

		//nested the_content hook
		$filter_counts = array_count_values($wp_current_filter);
		if(!empty($filter_counts['the_content']) && $filter_counts['the_content'] > 1) {
			return $content;
		}
    }
    
    $has_run = true;

	return $content . '<div class="ns-content-marker" style="display: none;"></div>';
}

//highlight inline js
function novashare_highlight_js() {
	echo '(()=>{function e(){var e="";return window.getSelection?e=window.getSelection().toString():document.getSelection?e=document.getSelection().toString():document.selection&&(e=document.selection.createRange().text),e}function t(e){for(var t=document.querySelectorAll(".ns-content-marker"),n=0;n<t.length++;n++)if(t[n].parentNode.contains(e.target))return!0;return!1}document.addEventListener("DOMContentLoaded",function(n){var i=document.getElementById("ns-highlight-share"),l=i.querySelectorAll("a.ns-button");l.forEach(function(e){e.dataset.highlight=e.href}),document.addEventListener("mouseup",function(n){setTimeout(function(){if(!t(n)){i.classList.remove("ns-visible");return}var o=e().trim();if(""!=o){var s=window.getSelection().getRangeAt(0).getBoundingClientRect(),r=s.left+window.scrollX+s.width/2-40*l.length/2,a=s.top+window.scrollY-40-15;i.style.left=(r>15?r:15)+"px",i.style.top=a+"px",l.forEach(function(e){e.href=e.dataset.highlight.replace("%highlight%",o)}),i.classList.add("ns-visible")}else i.classList.remove("ns-visible")},1)}),document.addEventListener("mousedown",function(e){i.contains(e.target)||i.classList.remove("ns-visible")})})})();';
}