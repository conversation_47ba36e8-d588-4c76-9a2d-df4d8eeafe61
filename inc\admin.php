<?php
//settings wrapper
echo '<div id="novashare-admin" class="wrap">';

	//hidden h2 for admin notice placement
	echo '<h2 style="display: none;"></h2>';

	//flex container
	echo '<div id="novashare-admin-container">';

		echo '<div id="novashare-admin-header">';

			//header
			echo '<div  class="novashare-admin-block">';

				echo '<div id="novashare-logo-bar">';

					//logo
					echo '<svg id="novashare-logo" viewBox="0 0 486 80"><path d="M826.219 973.874c-6.945.101-13.158 2.087-18.091 5.653v-4.512h-14.411v70.965h14.537v-39.03c0-10.889 7.325-18.563 17.812-18.665 5.852-.107 10.518 1.523 13.585 4.56 3.005 2.976 4.529 7.335 4.529 12.955v40.18h14.537v-40.3c0-19.323-12.732-31.806-32.498-31.806m75.897 58.576c-10.741 0-21.578-6.75-21.578-21.83 0-13.299 8.874-22.589 21.578-22.589s21.576 9.29 21.576 22.589c0 15.08-10.837 21.83-21.576 21.83m0-58.576c-20.853 0-35.987 15.454-35.987 36.746 0 21 15.134 36.24 35.987 36.24 20.852 0 35.987-15.24 35.987-36.24 0-21.292-15.135-36.746-35.987-36.746m74.347 49.786-20.036-48.645h-15.89l29.733 70.965h12.386l29.734-70.965h-15.892zm73.797 8.92c-12.43 0-21.45-9.29-21.45-22.09 0-12.718 9.02-21.953 21.45-21.953 15.1 0 21.99 11.415 21.99 22.023 0 5.84-1.95 11.25-5.49 15.23-3.94 4.44-9.65 6.79-16.5 6.79m21.45-57.565v6.513c-5.81-4.627-13.35-7.275-21.45-7.275-20.52 0-35.99 15.581-35.99 36.237 0 20.81 15.47 36.5 35.99 36.5 8.18 0 15.52-2.59 21.45-7.44v6.43h14.53v-70.965zm58.26 28.075-2.21-.38c-8.03-1.41-14.69-2.942-14.82-7.309-.04-1.367.4-2.471 1.38-3.472 2.36-2.403 7.42-3.824 13.35-3.771 6.47 0 11.52.929 16.78 5.555l3.03 2.665 9.41-10.033-3.04-2.848c-6.71-6.301-15.52-9.496-26.13-9.496-9.86-.092-18.42 2.786-23.55 7.909-3.66 3.663-5.56 8.432-5.51 13.792.16 16.318 16.15 18.978 27.85 20.928 11.8 1.9 17.04 3.53 16.91 8.16-.27 7.14-10.84 7.91-15.37 7.91-6.46 0-14.98-2.32-19.53-8.86l-2.67-3.85-10.75 9.55 1.95 3.01c5.9 9.04 17.44 14.44 30.88 14.44 18.08 0 29.53-8.26 29.89-21.48.77-17.62-17.14-20.63-27.85-22.42m70.15-28.963c-7.13.103-13.38 2.103-18.35 5.779v-31.01h-14.28v97.084h14.53v-38.53c0-11.031 7.64-19.039 18.16-19.039 10.76 0 15.99 5.73 15.99 17.519v40.05h14.42v-40.05c0-19.616-11.66-31.803-30.47-31.803m74.88 58.453c-12.43 0-21.45-9.29-21.45-22.09 0-12.718 9.02-21.953 21.45-21.953 15.11 0 21.99 11.415 21.99 22.023 0 5.84-1.95 11.25-5.49 15.23-3.94 4.44-9.64 6.79-16.5 6.79m21.45-51.052c-5.8-4.627-13.34-7.275-21.45-7.275-20.51 0-35.99 15.581-35.99 36.237 0 20.81 15.48 36.5 35.99 36.5 8.19 0 15.53-2.59 21.45-7.44v6.43h14.54v-70.965h-14.54zm55.71-7.147c-6.52 0-12.34 1.775-16.88 4.991l-.1-4.357h-14.06v70.965h14.53v-40.68c0-9.3 7.27-16.661 16.51-16.763 3.14 0 5.99.774 8.47 2.299l3.72 2.292 6.82-12.398-3.41-2.069c-4.93-2.989-10.21-4.433-15.6-4.28m35.58 29.419c2.6-9.599 10.4-15.769 20.54-15.769 11.82 0 19.22 5.685 20.86 15.769zm47.59-18.955c-6.34-6.993-15.94-10.844-27.05-10.844-20.52 0-35.99 15.689-35.99 36.489 0 21.08 15.14 36.37 35.99 36.37 12.27 0 23.89-5.24 30.32-13.68l2.46-3.24-10.95-8.76-2.58 3.42c-3.49 4.62-11.41 7.85-19.25 7.85-8.61 0-17.69-4.64-20.54-14.88h55.25l.37-3.75c1.17-11.87-1.61-21.891-8.03-28.975m-770.316 23.075c-1.273.42-2.537.83-3.812 1.24-.748.25-1.507.49-2.257.72-2.976-2.4-7.071-3.3-10.969-2.01-6.124 2-9.467 8.6-7.459 14.72 2.007 6.12 8.601 9.47 14.725 7.46a11.64 11.64 0 0 0 7.686-8.22c2.007-.63 4.01-1.28 6.013-1.93 2.538-.83 35.736-11.81 64.595-26.117 1.618 31.837-18.074 62.077-49.835 72.477-25.587 8.38-52.472 1.75-71.124-15.09a71 71 0 0 1-9.889-10.92c-23.867 3.97-38.699 3.47-42.306.8 1.263-4.07 11.859-12.55 31.386-22.73 16.331-8.54 38.909-18.3 67.559-27.679 1.993-.65 4.006-1.303 6.021-1.936 2.976 2.404 7.072 3.303 10.98 2.019 6.124-2.007 9.467-8.6 7.459-14.725-2.008-6.134-8.601-9.467-14.726-7.469a11.62 11.62 0 0 0-7.668 8.212 394 394 0 0 0-5.983 1.922c-2.615.856-37.058 12.239-66.318 26.946-3.877-33.534 16.167-66.275 49.464-77.186 27.327-8.95 56.163-.784 74.856 18.669a68 68 0 0 1 8.655 10.941c23.556-3.87 38.311-3.142 41.873-.519-1.277 4.136-12.157 12.784-32.244 23.188-16.262 8.427-38.557 18.007-66.682 27.217" style="fill:#4d4595" transform="translate(-307.878 -508.1)scale(.54942)"/></svg>';

					//menu toggle
					echo '<a href="#" id="novashare-menu-toggle"><span class="dashicons dashicons-menu"></span></a>';
				echo '</div>';

				//menu
				echo '<div id="novashare-menu">';

					if(!is_network_admin()) {

						$networks = novashare_networks();

						//options
						echo '<a href="#" rel="options-inline" class="active"><span class="dashicons dashicons-align-left"></span>' . __('Inline Content', 'novashare') . '</a>';
						echo '<a href="#floating" rel="options-floating"><span class="dashicons dashicons-laptop"></span>' . __('Floating Bar', 'novashare') . '</a>';
						echo '<a href="#share" rel="options-share"><span class="dashicons dashicons-share"></span>' . __('Share Button', 'novashare') . '</a>';
						echo '<a href="#ctt" rel="options-ctt"><span class="dashicons">' . $networks['twitter']['icon'] . '</span>' . __('Click to Post', 'novashare') . '</a>';
						echo '<a href="#highlight" rel="options-highlight"><span class="dashicons dashicons-admin-customizer"></span>' . __('Highlight', 'novashare') . '</a>';
						echo '<a href="#pinterest" rel="options-pinterest"><span class="dashicons dashicons-pinterest"></span>' . __('Pinterest', 'novashare') . '</a>';
						echo '<a href="#config" rel="options-config"><span class="dashicons dashicons-admin-generic"></span>' . __('Configuration', 'novashare') . '</a>';

						//spacer
						echo '<hr style="border-top: 1px solid #f2f2f2; border-bottom: 0px; margin: 10px 0px;" />';

						//tools
						echo '<a href="#tools" rel="tools-plugin"><span class="dashicons dashicons-admin-tools"></span>' . __('Tools', 'novashare') . '</a>';
					}
					else {

						//network
						echo '<a href="#" rel="network-network" class="active"><span class="dashicons dashicons-networking"></span>' . __('Network', 'novashare') . '</a>';
					}

					//license
					if(!is_multisite() || is_network_admin()) {
						echo '<a href="#license" rel="license-license"><span class="dashicons dashicons-admin-network"></span>' . __('License', 'novashare') . '</a>';
					}

					//support
					echo '<a href="#support" rel="support-support"><span class="dashicons dashicons-editor-help"></span>' . __('Support', 'novashare') . '</a>';

				echo '</div>';
			echo '</div>';

			//cta
			if(!get_option('novashare_close_cta')) {
				echo '<a href="https://perfmatters.io/novashare-discount/?utm_campaign=plugin-cta&utm_source=novashare" target="_blank" id="novashare-cta" class="novashare-admin-block novashare-mobile-hide">';
					echo '<span class="dashicons dashicons-tag" style="margin-right: 10px;"></span>';
					echo '<span>' . __('Get 25% off our performance plugin.', 'novashare') . '</span>';
					echo '<span id="novashare-cta-close" class="dashicons dashicons-no-alt"></span>';
				echo '</a>';
			}

		echo '</div>';

		echo '<div style="flex-grow: 1;">';
			echo '<div class="novashare-admin-block">';

				//version number
				echo '<span id="novashare-version" class="novashare-mobile-hide">' . __('Version', 'novashare') . ' ' . NOVASHARE_VERSION . '</span>';

				if(!is_network_admin()) {

					//get options
					$novashare = get_option('novashare');

					echo '<form method="post" id="novashare-options-form"enctype="multipart/form-data" data-ns-option="options">';

						//inline content
						echo '<section id="options-inline" class="section-content active">';
							novashare_settings_section('novashare', 'inline', 'dashicons-align-left');
							novashare_settings_section('novashare', 'inline_display');
							novashare_settings_section('novashare', 'inline_design');
							novashare_settings_section('novashare', 'inline_share_counts');
							novashare_settings_section('novashare', 'inline_cta');
						echo '</section>';

						//floating bar
						echo '<section id="options-floating" class="section-content">';
							novashare_settings_section('novashare', 'floating', 'dashicons-laptop');
							novashare_settings_section('novashare', 'floating_display');
							novashare_settings_section('novashare', 'floating_design');
							novashare_settings_section('novashare', 'floating_mobile', '', 'floating-hide_below_breakpoint' . (!empty($novashare['floating']['hide_below_breakpoint']) ? ' hidden' : ''));
							novashare_settings_section('novashare', 'floating_share_counts');
						echo '</section>';

						//share button
						echo '<section id="options-share" class="section-content">';
							novashare_settings_section('novashare', 'share', 'dashicons-share');
							novashare_settings_section('novashare', 'share_cta');
							novashare_settings_section('novashare', 'share_design');
							novashare_settings_section('novashare', 'share_share_counts');
						echo '</section>';
						
						//click to tweet
						echo '<section id="options-ctt" class="section-content">';
							novashare_settings_section('novashare', 'click_to_tweet', 'novashare-dashicons-x');
						echo '</section>';

						//highlight
						echo '<section id="options-highlight" class="section-content">';
							novashare_settings_section('novashare', 'highlight', 'dashicons-admin-customizer');
							novashare_settings_section('novashare', 'highlight_display');
							novashare_settings_section('novashare', 'highlight_design');
						echo '</section>';

						//pinterest
						echo '<section id="options-pinterest" class="section-content">';
							novashare_settings_section('novashare', 'pinterest', 'dashicons-pinterest');
							novashare_settings_section('novashare', 'pinterest_image_pins');
						echo '</section>';
						
						//configuration
						echo '<section id="options-config" class="section-content">';
							novashare_settings_section('novashare', 'config', 'dashicons-admin-generic');
							novashare_settings_section('novashare', 'config_meta');
							novashare_settings_section('novashare', 'config_share_counts');
							novashare_settings_section('novashare', 'config_ga');
							novashare_settings_section('novashare', 'config_link_shortening');
							novashare_settings_section('novashare', 'config_share_count_recovery');
						echo '</section>';

						//tools
						echo '<section id="tools-plugin" class="section-content">';
					    	novashare_settings_section('novashare_tools', 'novashare_tools', 'dashicons-admin-tools');

					    	//add migrator settings if needed
							if(function_exists('novashare_migrator_settings')) {
								novashare_settings_section('novashare_tools', 'migrate');
							}
					    echo '</section>';

					    //save
						echo '<div id="novashare-save" style="margin-top: 20px;">';
							novashare_action_button('save_settings', __('Save Changes', 'novashare'));
					    echo '</div>';

					echo '</form>';

				}
				else {

					//network
					echo '<section id="network-network" class="section-content active">';		
						require_once('network.php');
					echo '</section>';
				}

				//license
				if(!is_multisite() || is_network_admin()) {
					echo '<section id="license-license" class="section-content">';					
						require_once('license.php');
					echo '</section>';
				}

				//support
				echo '<section id="support-support" class="section-content">';	
					require_once('support.php');
				echo '</section>';

				//display correct section based on URL anchor
				echo '<script>
					!(function (t) {
					    var a = t.trim(window.location.hash);
					    if (a) {
					    	t("#novashare-menu > a.active").removeClass("active");
					    	var selectedNav = t(\'#novashare-menu > a[href="\' + a + \'"]\');
					    	t("#novashare-options-form").attr("data-ns-option", selectedNav.attr("rel").split("-")[0]); 
					    	t(selectedNav).addClass("active");
					    	var activeSection = t("#novashare-options-form .section-content.active");
					    	activeSection.removeClass("active");
					    	t("#" + selectedNav.attr("rel")).addClass("active");
					    }
					})(jQuery);
				</script>';
			echo '</div>';
		echo '</div>';
	echo '</div>';
echo '</div>';