# Novashare Shortlink

A WordPress plugin that modifies the Novashare social sharing plugin to use post ID-based short URLs (/?p=123) instead of full permalinks when sharing content.

## Description

The Novashare Shortlink plugin enhances the popular Novashare social sharing plugin by automatically converting share URLs to use WordPress's built-in short URL format instead of full permalinks.

### Key Features

- ✅ Automatically converts share URLs to short format
- ✅ Works seamlessly with Novashare plugin
- ✅ Lightweight and efficient
- ✅ No configuration required
- ✅ Compatible with all post types
- ✅ Admin notice when Novashare is not active

### How it works

When visitors use Novashare's sharing buttons, instead of sharing the full permalink like:
```
https://yoursite.com/2024/01/15/my-awesome-post/
```

They will share the shorter version:
```
https://yoursite.com/?p=123
```

This creates cleaner, shorter URLs for social media sharing while maintaining full functionality.

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Novashare plugin (free or pro version)

## Installation

### Method 1: WordPress Admin (Recommended)
1. Download the plugin zip file
2. Go to WordPress Admin → Plugins → Add New
3. Click "Upload Plugin" and select the zip file
4. Click "Install Now" and then "Activate"

### Method 2: Manual Installation
1. Upload the plugin files to `/wp-content/plugins/novashare-shortlink/`
2. Activate the plugin through the 'Plugins' menu in WordPress

### Method 3: Git Clone (for developers)
```bash
cd /path/to/wordpress/wp-content/plugins/
git clone https://github.com/yourusername/novashare-shortlink.git
```

## Usage

Once activated, the plugin works automatically. No configuration is needed.

1. Make sure Novashare plugin is installed and activated
2. Activate the Novashare Shortlink plugin
3. Share URLs will automatically use the short format

## FAQ

**Q: Does this plugin require Novashare to be installed?**
A: Yes, this plugin is specifically designed to work with the Novashare social sharing plugin.

**Q: Will this affect my SEO?**
A: No, this only affects the URLs used for social sharing. Your actual post URLs and SEO remain unchanged.

**Q: Does this work with custom post types?**
A: Yes, the plugin works with all post types that support singular views.

## Development

### File Structure
```
novashare-shortlink/
├── novashare-shortlink.php    # Main plugin file
├── readme.txt                 # WordPress.org readme
├── README.md                  # GitHub readme
└── uninstall.php             # Cleanup on uninstall
```

### Hooks and Filters

The plugin uses the following WordPress hooks:
- `plugins_loaded` - Initialize the plugin
- `admin_notices` - Show admin notices
- `novashare_get_current_url` - Filter to modify share URLs

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the GPL v2 or later - see the [LICENSE](LICENSE) file for details.

## Changelog

### 1.0.0
- Initial release
- Basic shortlink functionality for Novashare plugin
- Admin notice when Novashare is not active
- WordPress 5.0+ and PHP 7.4+ compatibility

## Support

For support, please create an issue on GitHub or contact [<EMAIL>].
