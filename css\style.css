/* General
/************************************************************/
.ns-buttons {
  position: relative;
}
.ns-buttons-wrapper {
  display: flex;
  flex-wrap: wrap;
}
.ns-columns.ns-has-total-share-count-before .ns-buttons-wrapper {
  margin-left: 50px;
}
.ns-columns.ns-has-total-share-count-after .ns-buttons-wrapper {
  margin-right: 50px;
}

/* Social Network Buttons
/************************************************************/
body a.ns-button {
  display: inline-flex;
  height: 40px;
  line-height: 40px;
  margin: 0px 10px 10px 0px;
  padding: 0px;
  font-size: 14px;
  overflow: hidden;
  text-decoration: none;
  border: none;
  --ns-button-color: #333;
  --ns-icon-color: #fff;
}
.ns-button * {
  pointer-events: none;
}
.ns-button-block {
  display: inline-flex;
  align-items: center;
  background: #333;
  background: var(--ns-button-color);
}
.ns-button-wrapper {
  display: inline-flex;
  align-items: center;
  overflow: hidden;
  width: 100%;
}
.ns-button:not(.ns-hover-swap):hover .ns-button-wrapper>span:not(.ns-inverse) {
   box-shadow: inset 0 0 0 50px rgba(0,0,0,0.1);
}
.ns-button:not(.ns-hover-swap):hover .ns-button-wrapper>span.ns-inverse {
  filter: brightness(0.9);
}

/* button icon */
.ns-button-icon {
  color: var(--ns-icon-color);
  height: 40px;
  width: 40px;
  min-width: 40px;
  box-sizing: border-box;
  justify-content: center;
}
.ns-button-icon > * {
  height: 50%;
  margin: 0 auto;
}
.ns-button-icon path {
  fill: currentColor;
}

/* button share count */
.ns-button-share-count {
  display: none;
  font-size: 12px;
  align-items: center;
}
.ns-button.ns-share-count:hover svg {
  display: none;
}
.ns-button.ns-share-count:hover .ns-button-share-count {
  display: flex;
}

/* button label */
.ns-button-label {
  color: var(--ns-icon-color);
  height: 100%;
  box-sizing: border-box;
  flex-grow: 1;
  padding: 0px 15px 0px 0px;
  overflow: hidden;
}
.ns-button-label-wrapper {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ns-button-icon.ns-inverse + .ns-button-label:not(.ns-inverse), .ns-button-icon:not(.ns-inverse) + .ns-button-label.ns-inverse, .ns-button-icon.ns-border + .ns-button-label.ns-inverse {
  padding-left: 10px;
}

/* Social Network Defaults
/************************************************************/
.ns-button.bluesky {
  --ns-button-color: #1185FE;
}
.ns-button.twitter {
  --ns-button-color: #000;
}
.ns-button.facebook {
  --ns-button-color: #3b5998;
}
.ns-button.linkedin {
  --ns-button-color: #0077B5;
}
.ns-button.pinterest {
  --ns-button-color: #C92228;
}
.ns-button.buffer {
  --ns-button-color: #323b43;
}
.ns-button.reddit {
  --ns-button-color: #ff4500;
}
.ns-button.hackernews {
  --ns-button-color: #F0652F;
}
.ns-button.pocket {
  --ns-button-color: #ef4056;
}
.ns-button.whatsapp {
  --ns-button-color: #25d366;
}
.ns-button.tumblr {
  --ns-button-color: #35465c;
}
.ns-button.vkontakte {
  --ns-button-color: #45668e;
}
.ns-button.xing {
  --ns-button-color: #026466;
}
.ns-button.flipboard {
  --ns-button-color: #e12828;
}
.ns-button.mix {
  --ns-button-color: #fd8235;
}
.ns-button.yummly {
  --ns-button-color: #e16120;
}
.ns-button.sms {
  --ns-button-color: #218AFF;
}
.ns-button.email {
  --ns-button-color: #319324;
}
.ns-button.print {
  --ns-button-color: #d34836;
}
.ns-button.\35 00px {
  --ns-button-color: #0099E5;
}
.ns-button.bandcamp {
  --ns-button-color: #408294;
}
.ns-button.behance {
  --ns-button-color: #1769FF;
}
.ns-button.bitbucket {
  --ns-button-color: #0052CC;
}
.ns-button.copy {
  --ns-button-color: #816B5B;
}
.ns-button.deviantart {
  --ns-button-color: #05CC47;
}
.ns-button.discord {
  --ns-button-color: #7289da;
}
.ns-button.dribbble {
  --ns-button-color: #EA4C89;
}
.ns-button.dropbox {
  --ns-button-color: #0061FF;
}
.ns-button.flickr {
  --ns-button-color: #0063DC;
}
.ns-button.foursquare {
  --ns-button-color: #F94877;
}
.ns-button.github {
  --ns-button-color: #181717;
}
.ns-button.goodreads {
  --ns-button-color: #553b08;
}
.ns-button.houzz {
  --ns-button-color: #4DBC15;
}
.ns-button.instagram {
  --ns-button-color: #E4405F;
}
.ns-button.jsfiddle {
  --ns-button-color: #0084FF;
}
.ns-button.lastfm {
  --ns-button-color: #D51007;
}
.ns-button.line {
  --ns-button-color: #00B900;
}
.ns-button.location {
  --ns-button-color: #EA4235;
}
.ns-button.mastodon {
  --ns-button-color: #6364FF;
}
.ns-button.messenger {
  --ns-button-color: #0078FF;
}
.ns-button.mixcloud {
  --ns-button-color: #314359;
}
.ns-button.nextdoor {
  --ns-button-color: #8ED500;
}
.ns-button.phone {
  --ns-button-color: #512DA8;
}
.ns-button.rss {
  --ns-button-color: #FFA500;
}
.ns-button.share {
  --ns-button-color: #2A2A2C;
}
.ns-button.skype {
  --ns-button-color: #00AFF0;
}
.ns-button.snapchat {
  --ns-button-color: #FFFC00;
}
.ns-button.soundcloud {
  --ns-button-color: #FF3300;
}
.ns-button.spotify {
  --ns-button-color: #1ED760;
}
.ns-button.stackoverflow {
  --ns-button-color: #FE7A16;
}
.ns-button.subscribe {
  --ns-button-color: #7A5189;
}
.ns-button.telegram {
  --ns-button-color: #0088cc;
}
.ns-button.tripadvisor {
  --ns-button-color: #34E0A1;
}
.ns-button.vimeo {
  --ns-button-color: #1AB7EA;
}
.ns-button.wordpress {
  --ns-button-color: #21759B;
}
.ns-button.yelp {
  --ns-button-color: #D32323;
}
.ns-button.youtube {
  --ns-button-color: #FF0000;
}
.ns-button.angellist, .ns-button.applemusic, .ns-button.codepen, .ns-button.digg, .ns-button.steam, .ns-button.threads, .ns-button.tiktok {
  --ns-button-color: #000;
}

/* Button Styles
/************************************************************/
.ns-border {
  border: 2px solid var(--ns-button-color);
}
body .ns-button .ns-button-block.ns-inverse, body .ns-button:hover .ns-button-block.ns-inverse, body a.ns-button.ns-hover-swap .ns-button-block.ns-button-wrapper {
  background: transparent;
  color: var(--ns-button-color);
}

/* button layout */
.ns-1-col .ns-buttons-wrapper > a {
  flex-basis: 100%;
  margin-right: 0;
}
.ns-2-col .ns-buttons-wrapper > a {
  flex-basis: calc(50% - 5px);
}
.ns-2-col .ns-buttons-wrapper > a:nth-of-type(2n) {
  margin-right: 0;
}
.ns-3-col .ns-buttons-wrapper > a {
  flex-basis: calc(33.333333% - 6.666666px);
}
.ns-3-col .ns-buttons-wrapper > a:nth-of-type(3n) {
  margin-right: 0;
}
.ns-4-col .ns-buttons-wrapper > a {
  flex-basis: calc(25% - 7.5px);
}
.ns-4-col .ns-buttons-wrapper > a:nth-of-type(4n) {
  margin-right: 0;
}
.ns-5-col .ns-buttons-wrapper > a {
  flex-basis: calc(20% - 8px);
}
.ns-5-col .ns-buttons-wrapper > a:nth-of-type(5n) {
  margin-right: 0;
}
.ns-6-col .ns-buttons-wrapper > a {
  flex-basis: calc(16.666666% - 8.333333px);
}
.ns-6-col .ns-buttons-wrapper > a:nth-of-type(6n) {
  margin-right: 0;
}

/* button size */
.ns-columns.small.ns-has-total-share-count-before .ns-buttons-wrapper {
  margin-left: 40px;
}
.ns-columns.small.ns-has-total-share-count-after .ns-buttons-wrapper {
  margin-right: 40px;
}
.ns-columns.large.ns-has-total-share-count-before .ns-buttons-wrapper {
  margin-left: 60px;
}
.ns-columns.large.ns-has-total-share-count-after .ns-buttons-wrapper {
  margin-right: 60px;
}
.ns-buttons.small .ns-button {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.ns-buttons.small .ns-button-icon {
  height: 30px;
  width: 30px;
  min-width: 30px;
}
.ns-buttons.small .ns-button-share-count {
  font-size: 10px;
}
.ns-buttons.large .ns-button {
  height: 50px;
  line-height: 50px;
  font-size: 18px;
}
.ns-buttons.large .ns-button-icon {
  height: 50px;
  width: 50px;
  min-width: 50px;
}
.ns-buttons.large .ns-button-share-count {
  font-size: 16px;
}

/* button shape */
.ns-rounded {
  border-radius: 5px;
}
.ns-rounded .ns-button-label {
  border-radius: 0px 5px 5px 0px;
}
.ns-circular {
  border-radius: 50px;
}
.ns-circular .ns-button-label {
  border-radius: 0px 50px 50px 0px;
}

/* button alignment */
.ns-align-right {
  justify-content: flex-end;
}
.ns-align-right .ns-button {
  margin: 0px 0px 10px 10px;
}
.ns-align-center {
  justify-content: center;
}
.ns-align-center .ns-button {
  margin-left: 5px;
  margin-right: 5px;
}

/* Total Share Count Box
/************************************************************/
.ns-total-share-count {
  font-size: 15px;
  line-height: normal;
  text-align: center;
  display: inline-table;
  height: 40px;
  width: 40px;
  margin: 0px 0px 10px 0px;
}
.ns-total-share-count-wrapper {
  display: table-cell;
  vertical-align: middle;
}
.ns-total-share-count-amount {
  font-weight: bold;
}
.ns-total-share-count-text {
  font-size: 8px;
  line-height: 11px;
}
.ns-columns .ns-total-share-count {
  position: absolute;
  right: 0;
  margin: 0px !important;
}
.ns-columns.ns-has-total-share-count-before .ns-total-share-count {
  right: unset;
  left: 0;
}

/* button sizes */
.ns-buttons.small .ns-total-share-count {
  height: 30px;
  width: 30px;
  font-size: 13px;
}
.ns-buttons.small .ns-total-share-count-text {
  font-size: 7px;
  line-height: 10px;
}
.ns-buttons.large .ns-total-share-count {
  height: 50px;
  width: 50px;
  font-size: 19px;
}
.ns-buttons.large .ns-total-share-count-text {
  font-size: 9px;
  line-height: 12px;
}

/* Inline Content
/************************************************************/
.ns-inline {
  margin-bottom: 10px;
}
.ns-inline-below {
  margin-top: 20px;
}
.ns-inline-cta {
  margin-bottom: 10px;
  font-size: 20px;
}

/* Floating Bar
/************************************************************/
.ns-floating {
  position: fixed;
  top: 25%;
  left: 5px;
  z-index: 999;
  height: 75%;
}
.ns-floating .ns-buttons-wrapper {
  flex-direction: column;
  height: 100%;
}

/* Misc
/************************************************************/
.ns-hide {
  display: none;
}
@media print {    
  .ns-no-print, .ns-no-print * {
    display: none !important;
  }
}