<?php
//share buttons block
function novashare_share_block($attributes, $content, $block) {

	//attribute check
	if(empty($attributes['id'])) {
		return;
	}

	//check for inner blocks	
	$block_count = !empty($block->inner_blocks) ? count($block->inner_blocks) : 0;
	if($block_count < 1) {
		return;
	}

	//setup instance
	$instance = array();
	$instance['id'] = $attributes['id'];
	$instance['style'] = $attributes['buttonStyle'] ?? '';
	$instance['layout'] = $attributes['buttonLayout'] ?? '';
	$instance['alignment'] = $attributes['alignment'] ?? '';
	$instance['shape'] = $attributes['buttonShape'] ?? '';
	$instance['size'] = $attributes['buttonSize'] ?? '';
	$instance['button_margin'] = $attributes['buttonMargin'] ?? '';
	$instance['labels'] = $attributes['showLabels'] ?? '';
	$instance['button_color'] = $attributes['buttonColor'] ?? '';
	$instance['button_hover_color'] = $attributes['buttonHoverColor'] ?? '';
	$instance['icon_color'] = $attributes['iconColor'] ?? '';
	$instance['icon_hover_color'] = $attributes['iconHoverColor'] ?? '';
	$instance['inverse_hover'] = $attributes['inverseHover'] ?? '';
	$instance['breakpoint'] = $attributes['mobileBreakpoint'] ?? '';
	$instance['hide_above_breakpoint'] = $attributes['hideAboveBreakpoint'] ?? '';
	$instance['hide_below_breakpoint'] = $attributes['hideBelowBreakpoint'] ?? '';
	$instance['total_share_count'] = $attributes['totalShareCount'] ?? '';
	$instance['total_share_count_position'] = $attributes['totalShareCountPosition'] ?? '';
	$instance['total_share_count_color'] = $attributes['totalShareCountColor'] ?? '';
	$instance['network_share_counts'] = $attributes['networkShareCounts'] ?? '';
	$instance['cta_text'] = $attributes['ctaText'] ?? '';
	$instance['cta_font_size'] = $attributes['ctaSize'] ?? '';
	$instance['cta_text_color'] = $attributes['ctaColor'] ?? '';

	//pass existing block class to wrapper
	if(!empty($block->attributes['className'])) {
		$instance['wrapper_class'] = $block->attributes['className'];
	}

	//loop through inner blocks
	for($i = 1; $i <= $block_count; $i++) {

		$inner_block = $block->inner_blocks->current(); 

		//add network to array
		if(!empty($inner_block->attributes['network'])) {
			$instance['social_networks'][] = $inner_block->attributes['network'];
		}

		$block->inner_blocks->next();
	}

	//return inline styles and buttons output
	return novashare_print_inline_styles('block-' . $instance['id'], $instance) . novashare_print_buttons('block-' . $instance['id'], $instance);
}