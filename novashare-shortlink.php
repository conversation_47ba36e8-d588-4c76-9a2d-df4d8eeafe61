<?php
/**
 * Plugin Name: Novashare Shortlink
 * Plugin URI: https://azaramedia.com
 * Description: Modifies Novashare plugin to use post ID-based short URLs (/?p=123) instead of full permalinks when sharing content.
 * Version: 1.0.4
 * Author: Azara Media
 * Author URI: https://azaramedia.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: novashare-shortlink
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('NOVASHARE_SHORTLINK_VERSION', '1.0.3');
define('NOVASHARE_SHORTLINK_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('NOVASHARE_SHORTLINK_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main plugin class
 */
class Novashare_Shortlink {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Novashare plugin is active
        if (!$this->is_novashare_active()) {
            add_action('admin_notices', array($this, 'novashare_missing_notice'));
            return;
        }

        // Load text domain for translations
        load_plugin_textdomain('novashare-shortlink', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Add the shortlink filters
        add_filter('novashare_post_permalink', array($this, 'modify_share_url'));
        add_filter('novashare_permalink', array($this, 'modify_share_url'));

        // Hook into get_the_permalink globally to catch copy button URLs
        add_filter('get_the_permalink', array($this, 'modify_permalink_for_copy_button'), 999, 2);

        // Also try to hook into the output generation
        add_action('wp_footer', array($this, 'fix_copy_button_with_js'), 999);
    }

    /**
     * Check if Novashare plugin is active
     */
    private function is_novashare_active() {
        return function_exists('novashare_load_textdomain') ||
               defined('NOVASHARE_VERSION') ||
               function_exists('novashare_print_buttons');
    }

    /**
     * Override Novashare share URL to use post ID-based short link
     */
    public function modify_share_url($url) {
        // Get the current post
        global $post;

        // Only modify if we have a valid post with an ID
        if ($post && isset($post->ID) && is_numeric($post->ID) && $post->ID > 0) {
            return home_url('/?p=' . $post->ID);
        }

        // If no valid post, try to get post ID from URL
        if (is_singular()) {
            $post_id = get_queried_object_id();
            if ($post_id && is_numeric($post_id) && $post_id > 0) {
                return home_url('/?p=' . $post_id);
            }
        }

        return $url;
    }

    /**
     * Modify get_the_permalink specifically for Novashare copy button
     * This catches the copy button URL generation in novashare_print_buttons()
     */
    public function modify_permalink_for_copy_button($permalink, $post_id) {
        // Only modify if we have a valid post ID
        if (!$post_id || !is_numeric($post_id) || $post_id <= 0) {
            return $permalink;
        }

        // Check if this is being called from Novashare context
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 20);
        foreach ($backtrace as $trace) {
            // Check for Novashare functions or files
            if (isset($trace['function']) &&
                (strpos($trace['function'], 'novashare') !== false ||
                 $trace['function'] === 'novashare_print_buttons' ||
                 $trace['function'] === 'novashare_inline_content' ||
                 $trace['function'] === 'novashare_inline_content_shortcode')) {
                return home_url('/?p=' . $post_id);
            }

            // Check for Novashare files
            if (isset($trace['file']) && strpos($trace['file'], 'novashare') !== false) {
                return home_url('/?p=' . $post_id);
            }
        }

        return $permalink;
    }

    /**
     * JavaScript fallback to fix copy button URLs
     * This runs in wp_footer as a last resort
     */
    public function fix_copy_button_with_js() {
        if (!is_singular()) {
            return;
        }

        global $post;
        if (!$post || !isset($post->ID)) {
            return;
        }

        $short_url = home_url('/?p=' . $post->ID);
        ?>
        <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for Novashare to load
            setTimeout(function() {
                // Find all copy buttons and fix their URLs
                var copyButtons = document.querySelectorAll('.ns-button[onclick*="writeText"]');
                copyButtons.forEach(function(button) {
                    var onclick = button.getAttribute('onclick');
                    if (onclick && onclick.includes('writeText')) {
                        // Replace the URL in writeText with our short URL
                        var newOnclick = onclick.replace(/writeText\([^)]+\)/, 'writeText("<?php echo esc_js($short_url); ?>")');
                        button.setAttribute('onclick', newOnclick);
                    }
                });
            }, 100);
        });
        </script>
        <?php
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            wp_die(__('This plugin requires WordPress version 5.0 or higher.', 'novashare-shortlink'));
        }

        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            wp_die(__('This plugin requires PHP version 7.4 or higher.', 'novashare-shortlink'));
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
    }

    /**
     * Admin notice when Novashare is not active
     */
    public function novashare_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php
                echo sprintf(
                    __('Novashare Shortlink plugin requires the %s plugin to be installed and activated.', 'novashare-shortlink'),
                    '<strong>Novashare</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }
}

// Initialize the plugin
new Novashare_Shortlink();