<?php
/**
 * Override Novashare share URL to use post ID-based short link
 */

function custom_novashare_shortlink($url) {
    // Only modify if inside the loop and it's a singular post/page
    if (is_singular() && in_the_loop() && is_main_query()) {
        global $post;
        if ($post && is_numeric($post->ID)) {
            return home_url('/?p=' . $post->ID);
        }
    }
    return $url;
}
add_filter('novashare_get_current_url', 'custom_novashare_shortlink');