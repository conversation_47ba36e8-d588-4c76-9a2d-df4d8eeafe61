=== Novashare Shortlink ===
Contributors: yourusername
Tags: novashare, shortlink, sharing, social media, url
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.1
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Modifies Novashare plugin to use post ID-based short URLs (/?p=123) instead of full permalinks when sharing content.

== Description ==

The Novashare Shortlink plugin enhances the popular Novashare social sharing plugin by automatically converting share URLs to use WordPress's built-in short URL format (/?p=123) instead of full permalinks.

**Key Features:**

* Automatically converts share URLs to short format
* Works seamlessly with Novashare plugin
* Lightweight and efficient
* No configuration required
* Compatible with all post types

**How it works:**

When visitors use Novashare's sharing buttons, instead of sharing the full permalink like:
`https://yoursite.com/2024/01/15/my-awesome-post/`

They will share the shorter version:
`https://yoursite.com/?p=123`

This creates cleaner, shorter URLs for social media sharing while maintaining full functionality.

**Requirements:**

* WordPress 5.0 or higher
* PHP 7.4 or higher
* Novashare plugin (free or pro version)

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/novashare-shortlink` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Make sure you have the Novashare plugin installed and activated.
4. That's it! The plugin will automatically start converting share URLs to short format.

== Frequently Asked Questions ==

= Does this plugin require Novashare to be installed? =

Yes, this plugin is specifically designed to work with the Novashare social sharing plugin. It will display an admin notice if Novashare is not detected.

= Will this affect my SEO? =

No, this only affects the URLs used for social sharing. Your actual post URLs and SEO remain unchanged.

= Does this work with custom post types? =

Yes, the plugin works with all post types that support singular views.

= Can I disable the shortlink for specific posts? =

Currently, the plugin applies to all posts automatically. Custom filtering options may be added in future versions.

== Screenshots ==

1. Before: Full permalink in share URL
2. After: Short URL format in share URL

== Changelog ==

= 1.0.1 =
* Fixed: Updated to use correct Novashare filter hooks (novashare_post_permalink and novashare_permalink)
* Improved: More robust post ID detection logic
* Enhanced: Better fallback handling when post context is not available

= 1.0.0 =
* Initial release
* Basic shortlink functionality for Novashare plugin
* Admin notice when Novashare is not active
* WordPress 5.0+ and PHP 7.4+ compatibility

== Upgrade Notice ==

= 1.0.1 =
Important fix: Updates filter hooks to work correctly with Novashare plugin. Please update immediately.

= 1.0.0 =
Initial release of Novashare Shortlink plugin.
