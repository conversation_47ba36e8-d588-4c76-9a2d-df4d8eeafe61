/**
 * Custom Social Share Plugin Styles
 * Version: 1.0.0
 */

/* Main container */
.custom-social-share {
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Buttons wrapper */
.css-buttons-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

/* Individual button styles */
.css-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background-color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
    box-sizing: border-box;
}

.css-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #ffffff;
}

.css-button:active {
    transform: translateY(0);
}

/* Icon styles */
.css-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.css-icon svg {
    width: 100%;
    height: 100%;
}

/* Label styles */
.css-label {
    white-space: nowrap;
}

/* Platform-specific colors */
.css-facebook {
    background-color: #1877f2;
}

.css-facebook:hover {
    background-color: #166fe5;
    color: #ffffff;
}

.css-twitter {
    background-color: #000000;
}

.css-twitter:hover {
    background-color: #1a1a1a;
    color: #ffffff;
}

.css-linkedin {
    background-color: #0a66c2;
}

.css-linkedin:hover {
    background-color: #0958a5;
    color: #ffffff;
}

.css-copy {
    background-color: #059669;
}

.css-copy:hover {
    background-color: #047857;
    color: #ffffff;
}

.css-copy.copied {
    background-color: #10b981;
}

/* Style variations */

/* Minimal style */
.css-style-minimal .css-button {
    background-color: transparent;
    color: #374151;
    border: 2px solid #d1d5db;
    padding: 10px 14px;
}

.css-style-minimal .css-facebook {
    color: #1877f2;
    border-color: #1877f2;
}

.css-style-minimal .css-facebook:hover {
    background-color: #1877f2;
    color: #ffffff;
}

.css-style-minimal .css-twitter {
    color: #000000;
    border-color: #000000;
}

.css-style-minimal .css-twitter:hover {
    background-color: #000000;
    color: #ffffff;
}

.css-style-minimal .css-linkedin {
    color: #0a66c2;
    border-color: #0a66c2;
}

.css-style-minimal .css-linkedin:hover {
    background-color: #0a66c2;
    color: #ffffff;
}

.css-style-minimal .css-copy {
    color: #059669;
    border-color: #059669;
}

.css-style-minimal .css-copy:hover {
    background-color: #059669;
    color: #ffffff;
}

/* Rounded style */
.css-style-rounded .css-button {
    border-radius: 50px;
    padding: 12px 20px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .css-buttons-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .css-button {
        justify-content: center;
        width: 100%;
        margin-bottom: 8px;
    }
    
    .css-button:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 480px) {
    .css-button {
        padding: 14px 16px;
        font-size: 16px;
    }
    
    .css-icon {
        width: 22px;
        height: 22px;
    }
}

/* Icon-only mode (when labels are hidden) */
.custom-social-share .css-button:not(:has(.css-label)) {
    padding: 12px;
    width: 44px;
    height: 44px;
    justify-content: center;
}

/* Alternative for browsers that don't support :has() */
.css-buttons-wrapper:not(:has(.css-label)) .css-button {
    padding: 12px;
    width: 44px;
    height: 44px;
    justify-content: center;
}

/* Loading state for copy button */
.css-copy.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Success message for copy button */
.css-copy-success {
    position: absolute;
    background-color: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1000;
}

.css-copy-success.show {
    opacity: 1;
}

/* Accessibility improvements */
.css-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.css-button:focus:not(:focus-visible) {
    outline: none;
}

/* Print styles */
@media print {
    .custom-social-share {
        display: none;
    }
}
