/**
 * Custom Social Share Plugin JavaScript
 * Version: 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Handle copy button clicks
    const copyButtons = document.querySelectorAll('.css-copy');
    
    copyButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const url = button.getAttribute('data-url');
            if (!url) {
                console.error('Custom Social Share: No URL found for copy button');
                return;
            }
            
            // Add loading state
            button.classList.add('loading');
            
            // Try to copy to clipboard
            copyToClipboard(url, button);
        });
    });
    
    /**
     * Copy text to clipboard with fallback methods
     */
    function copyToClipboard(text, button) {
        // Method 1: Modern Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(function() {
                showCopySuccess(button, 'Link copied!');
            }).catch(function(err) {
                console.error('Custom Social Share: Clipboard API failed:', err);
                fallbackCopyMethod(text, button);
            });
        } else {
            // Method 2: Fallback for older browsers or non-HTTPS
            fallbackCopyMethod(text, button);
        }
    }
    
    /**
     * Fallback copy method using temporary textarea
     */
    function fallbackCopyMethod(text, button) {
        try {
            // Create temporary textarea
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.left = '-999999px';
            textarea.style.top = '-999999px';
            document.body.appendChild(textarea);
            
            // Select and copy
            textarea.focus();
            textarea.select();
            
            const successful = document.execCommand('copy');
            document.body.removeChild(textarea);
            
            if (successful) {
                showCopySuccess(button, 'Link copied!');
            } else {
                showCopyError(button, 'Copy failed');
            }
        } catch (err) {
            console.error('Custom Social Share: Fallback copy failed:', err);
            showCopyError(button, 'Copy not supported');
        }
    }
    
    /**
     * Show copy success message
     */
    function showCopySuccess(button, message) {
        // Remove loading state
        button.classList.remove('loading');
        
        // Add success state
        button.classList.add('copied');
        
        // Change button text temporarily if it has a label
        const label = button.querySelector('.css-label');
        let originalText = '';
        
        if (label) {
            originalText = label.textContent;
            label.textContent = 'Copied!';
        }
        
        // Show success tooltip
        showTooltip(button, message, 'success');
        
        // Reset after 2 seconds
        setTimeout(function() {
            button.classList.remove('copied');
            if (label && originalText) {
                label.textContent = originalText;
            }
        }, 2000);
    }
    
    /**
     * Show copy error message
     */
    function showCopyError(button, message) {
        // Remove loading state
        button.classList.remove('loading');
        
        // Show error tooltip
        showTooltip(button, message, 'error');
        
        // Fallback: prompt user to copy manually
        setTimeout(function() {
            const url = button.getAttribute('data-url');
            if (url) {
                prompt('Copy this link:', url);
            }
        }, 1000);
    }
    
    /**
     * Show tooltip message
     */
    function showTooltip(button, message, type) {
        // Remove any existing tooltips
        const existingTooltip = button.querySelector('.css-copy-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
        
        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'css-copy-tooltip css-copy-' + type;
        tooltip.textContent = message;
        tooltip.style.cssText = `
            position: absolute;
            background-color: ${type === 'success' ? '#10b981' : '#ef4444'};
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;
        
        // Position button relatively if not already
        const buttonStyle = window.getComputedStyle(button);
        if (buttonStyle.position === 'static') {
            button.style.position = 'relative';
        }
        
        // Add tooltip to button
        button.appendChild(tooltip);
        
        // Show tooltip
        setTimeout(function() {
            tooltip.style.opacity = '1';
        }, 10);
        
        // Hide tooltip after 3 seconds
        setTimeout(function() {
            tooltip.style.opacity = '0';
            setTimeout(function() {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Track social share clicks (optional analytics)
     */
    function trackShareClick(platform, url) {
        // You can add analytics tracking here
        console.log('Custom Social Share: ' + platform + ' share clicked for URL: ' + url);
        
        // Example: Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', 'share', {
                method: platform,
                content_type: 'article',
                item_id: url
            });
        }
        
        // Example: Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Share', {
                content_type: 'article',
                content_url: url
            });
        }
    }
    
    // Add click tracking to all share buttons
    const shareButtons = document.querySelectorAll('.css-button:not(.css-copy)');
    shareButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const platform = button.classList.contains('css-facebook') ? 'facebook' :
                           button.classList.contains('css-twitter') ? 'twitter' :
                           button.classList.contains('css-linkedin') ? 'linkedin' : 'unknown';
            
            const url = button.href || window.location.href;
            trackShareClick(platform, url);
        });
    });
    
    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            if (e.target.classList.contains('css-copy')) {
                e.preventDefault();
                e.target.click();
            }
        }
    });
    
});

/**
 * Utility function to get current page URL with short format
 */
function getCurrentShortUrl() {
    // Extract post ID from URL or use current URL
    const urlParams = new URLSearchParams(window.location.search);
    const postId = urlParams.get('p');
    
    if (postId) {
        return window.location.origin + '/?p=' + postId;
    }
    
    // Fallback to current URL
    return window.location.href;
}
