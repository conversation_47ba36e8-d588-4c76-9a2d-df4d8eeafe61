<?php
/**
 * Uninstall script for Novashare Shortlink plugin
 * 
 * This file is executed when the plugin is uninstalled (deleted) from WordPress.
 * It cleans up any data or settings created by the plugin.
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Currently, this plugin doesn't store any data in the database
// or create any files that need cleanup.
// 
// If future versions add options or data storage, cleanup code would go here.
// For example:
// delete_option('novashare_shortlink_settings');
// delete_site_option('novashare_shortlink_network_settings');

// Clear any cached data
wp_cache_flush();
